{"name": "thinking-patterns-mcp-server", "version": "1.0.0", "description": "MCP Server for AI Thinking Patterns - Universal coding best practices and patterns", "main": "mcp-thinking.js", "type": "module", "scripts": {"start": "node mcp-thinking.js", "dev": "node --inspect mcp-thinking.js", "test": "echo \"Error: no test specified\" && exit 1", "install-deps": "npm install @modelcontextprotocol/sdk"}, "keywords": ["mcp", "model-context-protocol", "ai", "coding-patterns", "best-practices", "code-quality", "software-engineering", "thinking-patterns", "universal-patterns"], "author": "<PERSON>", "license": "MIT", "dependencies": {"@modelcontextprotocol/sdk": "^0.5.0"}, "engines": {"node": ">=18.0.0"}, "bin": {"thinking-patterns-mcp": "./mcp-thinking.js"}, "mcp": {"server": {"name": "thinking-patterns", "description": "Universal coding patterns and best practices for AI assistants", "version": "1.0.0"}}}