#!/usr/bin/env node

/**
 * MCP Server for AI Thinking Patterns
 * Exposes universal coding patterns and best practices as MCP tools
 */

import { Server } from "@modelcontextprotocol/sdk/server/index.js";
import { StdioServerTransport } from "@modelcontextprotocol/sdk/server/stdio.js";
import {
  CallToolRequestSchema,
  ListToolsRequestSchema,
} from "@modelcontextprotocol/sdk/types.js";
import fs from "fs/promises";
import path from "path";

class ThinkingPatternsServer {
  constructor() {
    this.server = new Server(
      {
        name: "thinking-patterns-server",
        version: "1.0.0",
      },
      {
        capabilities: {
          tools: {},
        },
      }
    );

    this.setupToolHandlers();
  }

  setupToolHandlers() {
    // List available tools
    this.server.setRequestHandler(ListToolsRequestSchema, async () => {
      return {
        tools: [
          {
            name: "get_code_quality_rules",
            description:
              "Get specific code quality and readability rules with metrics",
            inputSchema: {
              type: "object",
              properties: {
                category: {
                  type: "string",
                  enum: [
                    "function_length",
                    "naming",
                    "nesting",
                    "comments",
                    "all",
                  ],
                  description:
                    "Specific category of code quality rules to retrieve",
                },
              },
            },
          },
          {
            name: "get_error_handling_strategy",
            description:
              "Get error handling patterns and strategies for different contexts",
            inputSchema: {
              type: "object",
              properties: {
                context: {
                  type: "string",
                  enum: [
                    "user_facing",
                    "system_library",
                    "api_integration",
                    "all",
                  ],
                  description: "Context for error handling strategy",
                },
                language: {
                  type: "string",
                  enum: [
                    "python",
                    "javascript",
                    "rust",
                    "go",
                    "java",
                    "universal",
                  ],
                  description: "Programming language context",
                },
              },
            },
          },
          {
            name: "get_function_design_rules",
            description:
              "Get function design principles including parameters, SRP, and purity",
            inputSchema: {
              type: "object",
              properties: {
                aspect: {
                  type: "string",
                  enum: ["parameters", "srp", "purity", "return_values", "all"],
                  description: "Specific aspect of function design",
                },
              },
            },
          },
          {
            name: "get_data_structure_guidance",
            description:
              "Get data structure selection guidance based on use case",
            inputSchema: {
              type: "object",
              properties: {
                use_case: {
                  type: "string",
                  enum: [
                    "fast_lookup",
                    "ordered_data",
                    "memory_efficient",
                    "concurrent",
                    "all",
                  ],
                  description: "Primary use case for data structure selection",
                },
              },
            },
          },
          {
            name: "get_testing_strategy",
            description:
              "Get ROI-optimized testing strategies and pyramid guidelines",
            inputSchema: {
              type: "object",
              properties: {
                focus: {
                  type: "string",
                  enum: [
                    "pyramid",
                    "coverage",
                    "tdd_bdd",
                    "maintenance",
                    "all",
                  ],
                  description: "Testing strategy focus area",
                },
              },
            },
          },
          {
            name: "get_architecture_patterns",
            description: "Get code organization and architecture patterns",
            inputSchema: {
              type: "object",
              properties: {
                pattern: {
                  type: "string",
                  enum: [
                    "feature_based",
                    "dependencies",
                    "separation_concerns",
                    "scalability",
                    "all",
                  ],
                  description: "Architecture pattern category",
                },
              },
            },
          },
          {
            name: "get_performance_guidelines",
            description:
              "Get performance optimization strategies and caching patterns",
            inputSchema: {
              type: "object",
              properties: {
                area: {
                  type: "string",
                  enum: [
                    "optimization_strategy",
                    "caching",
                    "resource_management",
                    "trade_offs",
                    "all",
                  ],
                  description: "Performance optimization area",
                },
              },
            },
          },
          {
            name: "get_security_patterns",
            description:
              "Get security-first development patterns and reliability engineering",
            inputSchema: {
              type: "object",
              properties: {
                domain: {
                  type: "string",
                  enum: [
                    "input_validation",
                    "least_privilege",
                    "secure_defaults",
                    "reliability",
                    "all",
                  ],
                  description: "Security/reliability domain",
                },
              },
            },
          },
          {
            name: "get_implementation_checklist",
            description: "Get implementation checklists for different phases",
            inputSchema: {
              type: "object",
              properties: {
                phase: {
                  type: "string",
                  enum: [
                    "pre_implementation",
                    "implementation",
                    "post_implementation",
                    "all",
                  ],
                  description: "Implementation phase",
                },
              },
            },
          },
          {
            name: "validate_code_against_patterns",
            description:
              "Validate code snippet against thinking patterns and provide recommendations",
            inputSchema: {
              type: "object",
              properties: {
                code: {
                  type: "string",
                  description: "Code snippet to validate",
                },
                language: {
                  type: "string",
                  description: "Programming language of the code",
                },
                focus_areas: {
                  type: "array",
                  items: {
                    type: "string",
                    enum: [
                      "readability",
                      "error_handling",
                      "function_design",
                      "performance",
                      "security",
                    ],
                  },
                  description: "Specific areas to focus validation on",
                },
              },
              required: ["code"],
            },
          },
        ],
      };
    });

    // Handle tool calls
    this.server.setRequestHandler(CallToolRequestSchema, async (request) => {
      const { name, arguments: args } = request.params;

      try {
        switch (name) {
          case "get_code_quality_rules":
            return await this.getCodeQualityRules(args?.category || "all");

          case "get_error_handling_strategy":
            return await this.getErrorHandlingStrategy(
              args?.context || "all",
              args?.language || "universal"
            );

          case "get_function_design_rules":
            return await this.getFunctionDesignRules(args?.aspect || "all");

          case "get_data_structure_guidance":
            return await this.getDataStructureGuidance(args?.use_case || "all");

          case "get_testing_strategy":
            return await this.getTestingStrategy(args?.focus || "all");

          case "get_architecture_patterns":
            return await this.getArchitecturePatterns(args?.pattern || "all");

          case "get_performance_guidelines":
            return await this.getPerformanceGuidelines(args?.area || "all");

          case "get_security_patterns":
            return await this.getSecurityPatterns(args?.domain || "all");

          case "get_implementation_checklist":
            return await this.getImplementationChecklist(args?.phase || "all");

          case "validate_code_against_patterns":
            return await this.validateCodeAgainstPatterns(
              args?.code,
              args?.language,
              args?.focus_areas
            );

          default:
            throw new Error(`Unknown tool: ${name}`);
        }
      } catch (error) {
        return {
          content: [
            {
              type: "text",
              text: `Error: ${error.message}`,
            },
          ],
        };
      }
    });
  }

  async getCodeQualityRules(category) {
    const rules = {
      function_length: {
        title: "Function Length Standards",
        rules: [
          "Optimal Range: 5-15 lines per function",
          "Hard Limit: 20 lines maximum",
          "Rationale: Functions >20 lines require 3x more mental effort to understand",
        ],
        metrics: "Functions >20 lines require 3x more mental effort",
      },
      naming: {
        title: "Variable Naming Requirements",
        rules: [
          "Use Full Words: calculateUserTotalScore() over calc()",
          "Avoid Abbreviations: Except well-known conventions (i, j for loops)",
          "Impact: Proper naming improves comprehension by 60%",
        ],
        metrics: "60% better comprehension with descriptive names",
      },
      nesting: {
        title: "Code Structure Limits",
        rules: [
          "Maximum Nesting: 3 levels of indentation",
          "Rationale: >3 levels cause 80% drop in code comprehension",
          "Alternative: Extract nested logic into separate functions",
        ],
        metrics: "80% drop in comprehension with >3 nesting levels",
      },
      comments: {
        title: "Comment Strategy",
        rules: [
          "Explain 'Why': Not 'what' - code should be self-documenting",
          "Document Complex Logic: When optimization sacrifices readability",
          "Avoid Obvious Comments: Don't comment what the code clearly shows",
        ],
      },
    };

    if (category === "all") {
      return {
        content: [
          {
            type: "text",
            text: JSON.stringify(rules, null, 2),
          },
        ],
      };
    }

    return {
      content: [
        {
          type: "text",
          text: JSON.stringify(rules[category] || {}, null, 2),
        },
      ],
    };
  }

  async getErrorHandlingStrategy(context, language) {
    const strategies = {
      user_facing: {
        title: "User-Facing Application Error Handling",
        internal: "Fail-fast with detailed logging for developers",
        external: "Graceful degradation with helpful user messages",
        example:
          "API timeout → Show cached data + 'Some features may be delayed'",
        impact: "Clear error messages reduce support tickets by 45%",
      },
      system_library: {
        title: "System/Library Code Error Handling",
        principle: "Let caller decide recovery strategy",
        pattern: "Return structured errors with context",
        example: "Result<Data, DatabaseError> instead of generic exceptions",
      },
      universal_rules: [
        "Log Everything: With appropriate detail level for debugging",
        "Never Expose Internal Details: To end users for security",
        "Provide Actionable Feedback: When possible, tell users what to do",
        "Implement Circuit Breakers: For external dependencies",
        "Use Structured Error Types: Instead of string messages",
      ],
    };

    const languagePatterns = {
      python: "try/except with specific exception types",
      javascript: "Promise.catch() and async/await with try/catch",
      rust: "Result<T,E> type system for explicit error handling",
      go: "Explicit error returns: if err != nil",
      java: "Checked exceptions with proper exception hierarchy",
    };

    const result = {
      context_strategy: strategies[context] || strategies,
      language_pattern:
        languagePatterns[language] || "Universal patterns apply",
      universal_rules: strategies.universal_rules,
    };

    return {
      content: [
        {
          type: "text",
          text: JSON.stringify(result, null, 2),
        },
      ],
    };
  }

  async getFunctionDesignRules(aspect) {
    const rules = {
      parameters: {
        title: "Parameter Optimization Rules",
        sweet_spot: "2-3 parameters for 90% of functions",
        four_plus: "Use configuration objects/structs for 4+ parameters",
        booleans:
          "Replace with enums for clarity (SortOrder.ASCENDING vs true)",
        order: "Most important first, optional parameters last",
      },
      srp: {
        title: "Single Responsibility Validation",
        one_reason: "Function should have exactly one reason to be modified",
        name_test: "If you can't name it without 'and', it's doing too much",
        line_count: "Functions >15 lines often violate SRP",
        complexity: "Maximum 10 decision points per function",
      },
      purity: {
        title: "Pure vs Impure Function Strategy",
        pure_for: "Business logic, calculations, transformations",
        impure_for:
          "I/O, logging, state mutations - isolate to specific layers",
        testing_impact: "Pure functions are 5x easier to test",
        documentation: "Clearly mark functions with side effects",
      },
      return_values: {
        title: "Return Value Consistency",
        type_consistency: "Always return same type or use union types",
        error_handling:
          "Use Result<T,E> pattern or consistent exception strategy",
        null_safety: "Avoid returning null, use Optional/Maybe types",
        documentation:
          "Return value meaning should be obvious from function name",
      },
    };

    return {
      content: [
        {
          type: "text",
          text: JSON.stringify(
            aspect === "all" ? rules : rules[aspect] || {},
            null,
            2
          ),
        },
      ],
    };
  }

  async getDataStructureGuidance(useCase) {
    const guidance = {
      fast_lookup: {
        title: "For Fast Lookups (O(1) access)",
        structures: [
          "Hash Maps/Dictionaries: Key-value pairs, unique keys",
          "Sets: Membership testing, duplicate removal",
          "Arrays with Index: When keys are sequential integers",
        ],
        tradeoff: "Memory overhead vs lookup speed",
      },
      ordered_data: {
        title: "For Ordered Data Requirements",
        structures: [
          "Sorted Arrays: Binary search O(log n), immutable preferred",
          "Balanced Trees: Insert/delete with order maintenance",
          "Priority Queues: When you need min/max element frequently",
          "Linked Lists: When frequent insertion/deletion at arbitrary positions",
        ],
      },
      memory_efficient: {
        title: "Memory Efficiency Patterns",
        techniques: [
          "Packed Arrays: For homogeneous data, 50-80% memory savings",
          "Bit Sets: For boolean flags, 8x memory reduction",
          "String Interning: For repeated strings, significant memory savings",
          "Lazy Loading: Load data structures on-demand",
        ],
      },
      concurrent: {
        title: "Concurrency Considerations",
        approaches: [
          "Immutable Structures: Safe by default, no locking needed",
          "Copy-on-Write: Good for read-heavy, write-light scenarios",
          "Lock-Free Structures: For high-performance concurrent access",
          "Thread-Local Storage: When each thread needs its own copy",
        ],
      },
      decision_tree: [
        "Need fast lookups? → Hash Map/Set",
        "Need ordering? → Sorted Array/Tree",
        "Frequent insertions/deletions? → Linked List/Tree",
        "Memory constrained? → Packed Array/Bit Set",
        "Concurrent access? → Immutable/Lock-Free structures",
      ],
    };

    return {
      content: [
        {
          type: "text",
          text: JSON.stringify(
            useCase === "all" ? guidance : guidance[useCase] || {},
            null,
            2
          ),
        },
      ],
    };
  }

  async getTestingStrategy(focus) {
    const strategy = {
      pyramid: {
        title: "Testing Pyramid Implementation",
        distribution: "70% Unit Tests, 20% Integration Tests, 10% E2E Tests",
        roi: "Unit tests provide 10x ROI compared to E2E tests",
        unit_focus: "Fast, isolated, test business logic",
        integration_focus: "Test component interactions",
        e2e_focus: "Test critical user journeys",
      },
      coverage: {
        title: "Coverage vs Quality Guidelines",
        sweet_spot: "80% code coverage provides optimal bug detection",
        diminishing_returns: ">90% coverage often tests trivial code",
        quality_focus: "Test complex business logic, not getters/setters",
        mutation_testing:
          "More valuable than line coverage for quality assessment",
      },
      tdd_bdd: {
        title: "TDD vs BDD Strategy",
        tdd_benefits:
          "40% fewer bugs, better design, slower initial development",
        bdd_benefits: "Better stakeholder communication, clearer requirements",
        hybrid_approach: "TDD for complex logic, BDD for user-facing features",
        context_matters: "TDD for libraries, BDD for applications",
      },
      maintenance: {
        title: "Test Maintenance Rules",
        avoid_brittle: "Don't test implementation details",
        maintenance_overhead: "Integration tests require 3x more maintenance",
        test_quality:
          "Treat test code with same quality standards as production",
        living_documentation: "Tests should serve as usage examples",
      },
    };

    return {
      content: [
        {
          type: "text",
          text: JSON.stringify(
            focus === "all" ? strategy : strategy[focus] || {},
            null,
            2
          ),
        },
      ],
    };
  }

  async validateCodeAgainstPatterns(
    code,
    language = "unknown",
    focusAreas = []
  ) {
    if (!code) {
      return {
        content: [
          {
            type: "text",
            text: "Error: No code provided for validation",
          },
        ],
      };
    }

    const validation = {
      code_snippet: code,
      language: language,
      analysis: {},
      recommendations: [],
      score: 0,
    };

    // Basic readability analysis
    const lines = code.split("\n").filter((line) => line.trim().length > 0);
    const functionMatches = code.match(/function|def |fn |func /g) || [];

    // Function length analysis
    if (lines.length > 20) {
      validation.analysis.function_length =
        "VIOLATION: Function appears to be >20 lines";
      validation.recommendations.push(
        "Break down into smaller functions (5-15 lines optimal)"
      );
    } else if (lines.length <= 15) {
      validation.analysis.function_length =
        "GOOD: Function length within optimal range";
      validation.score += 20;
    }

    // Nesting analysis
    const maxIndentation = Math.max(
      ...lines.map((line) => {
        const match = line.match(/^(\s*)/);
        return match ? match[1].length : 0;
      })
    );

    if (maxIndentation > 12) {
      // Assuming 4 spaces per level
      validation.analysis.nesting =
        "WARNING: Deep nesting detected (>3 levels)";
      validation.recommendations.push(
        "Extract nested logic into separate functions"
      );
    } else {
      validation.analysis.nesting = "GOOD: Nesting levels appropriate";
      validation.score += 20;
    }

    // Variable naming analysis
    const variableNames = code.match(/\b[a-z][a-zA-Z0-9]*\b/g) || [];
    const shortNames = variableNames.filter(
      (name) => name.length < 3 && !["i", "j", "k"].includes(name)
    );

    if (shortNames.length > 0) {
      validation.analysis.naming = `WARNING: Short variable names detected: ${shortNames.join(
        ", "
      )}`;
      validation.recommendations.push(
        "Use descriptive variable names for better readability"
      );
    } else {
      validation.analysis.naming = "GOOD: Variable names appear descriptive";
      validation.score += 20;
    }

    // Error handling analysis
    const hasErrorHandling = /try|catch|except|Result|Option|error|Error/.test(
      code
    );
    if (!hasErrorHandling && code.length > 100) {
      validation.analysis.error_handling =
        "WARNING: No error handling detected";
      validation.recommendations.push(
        "Add appropriate error handling for robustness"
      );
    } else if (hasErrorHandling) {
      validation.analysis.error_handling = "GOOD: Error handling present";
      validation.score += 20;
    }

    // Comments analysis
    const commentLines = lines.filter((line) => /^\s*(\/\/|#|\/\*)/.test(line));
    const commentRatio = commentLines.length / lines.length;

    if (commentRatio > 0.3) {
      validation.analysis.comments =
        "WARNING: High comment density - code may not be self-documenting";
      validation.recommendations.push(
        "Consider making code more self-documenting"
      );
    } else if (commentRatio > 0.1) {
      validation.analysis.comments = "GOOD: Appropriate comment density";
      validation.score += 20;
    }

    validation.overall_score = `${validation.score}/100`;
    validation.grade =
      validation.score >= 80
        ? "EXCELLENT"
        : validation.score >= 60
        ? "GOOD"
        : validation.score >= 40
        ? "NEEDS_IMPROVEMENT"
        : "POOR";

    return {
      content: [
        {
          type: "text",
          text: JSON.stringify(validation, null, 2),
        },
      ],
    };
  }

  async getArchitecturePatterns(pattern) {
    const patterns = {
      feature_based: {
        title: "Feature-Based Organization (Recommended)",
        benefits: [
          "40% faster onboarding for new developers",
          "60% easier feature location compared to layer-based",
          "Better scalability with team size",
          "Changes contained within feature boundaries",
        ],
        structure: {
          "src/": {
            "shared/": "Truly shared utilities",
            "user-management/":
              "Feature modules with components, services, types, tests",
            "payment-processing/": "Self-contained feature modules",
          },
        },
      },
      dependencies: {
        title: "Dependency Management Rules",
        rules: [
          "Features can depend on shared, not on each other",
          "Circular dependencies forbidden - indicates poor separation",
          "Interface segregation: Small, focused interfaces over large ones",
          "Dependency injection: Prefer composition over inheritance",
        ],
      },
      separation_concerns: {
        title: "Separation of Concerns Strategy",
        layers: {
          "Business Logic": "Pure functions, no I/O or UI concerns",
          "Data Layer": "Database/API interactions isolated",
          "Presentation Layer": "UI components, no business logic",
          Configuration: "Environment-specific settings centralized",
        },
      },
    };

    return {
      content: [
        {
          type: "text",
          text: JSON.stringify(
            pattern === "all" ? patterns : patterns[pattern] || {},
            null,
            2
          ),
        },
      ],
    };
  }

  async getPerformanceGuidelines(area) {
    const guidelines = {
      optimization_strategy: {
        title: "Strategic Performance Optimization",
        priority_order: [
          "Measure First: Profile before optimizing - 80% of time spent in 20% of code",
          "User-Facing Performance: Optimize what users actually experience",
          "Bottleneck Identification: Focus on actual constraints, not theoretical ones",
          "Business Impact: Optimize features that affect revenue/user satisfaction",
        ],
      },
      caching: {
        title: "Caching Strategy Matrix",
        strategies: {
          "In-Memory Caching": "For frequently accessed, rarely changed data",
          "Database Query Caching":
            "For expensive queries with predictable patterns",
          "CDN Caching":
            "For static assets and geographically distributed users",
          "Cache Invalidation": "Use time-based or event-driven strategies",
        },
      },
      resource_management: {
        title: "Resource Management Patterns",
        patterns: [
          "Connection Pooling: For database/network connections",
          "Object Pooling: For expensive-to-create objects",
          "Memory Pooling: For high-frequency allocations",
          "Thread Pooling: For concurrent task execution",
        ],
      },
      trade_offs: {
        title: "Performance vs Readability Trade-offs",
        guidelines: [
          "Readability Wins: Choose readable code unless performance is critical",
          "Document Optimizations: Complex optimizations need extensive comments",
          "Benchmark Driven: Only sacrifice readability with measurable gains",
          "Reversible Changes: Keep unoptimized version in comments/git history",
        ],
      },
    };

    return {
      content: [
        {
          type: "text",
          text: JSON.stringify(
            area === "all" ? guidelines : guidelines[area] || {},
            null,
            2
          ),
        },
      ],
    };
  }

  async getSecurityPatterns(domain) {
    const patterns = {
      input_validation: {
        title: "Input Validation & Sanitization",
        rules: [
          "Validate Early: Check inputs at system boundaries, not deep in logic",
          "Whitelist Approach: Define what's allowed, reject everything else",
          "Type Safety: Use strong typing to prevent category errors",
          "Sanitization: Clean data for specific contexts (HTML, SQL, etc.)",
        ],
      },
      least_privilege: {
        title: "Principle of Least Privilege in Code",
        applications: [
          "Minimal Permissions: Functions/modules access only what they need",
          "Scope Limitation: Variables and functions have minimal necessary scope",
          "API Surface: Expose minimal public interface, keep internals private",
          "Resource Access: Limit file system, network, and database access",
        ],
      },
      secure_defaults: {
        title: "Secure by Default Patterns",
        principles: [
          "Safe Defaults: Choose secure options as defaults, allow opt-out",
          "Fail Securely: When security checks fail, deny access by default",
          "Configuration: Security settings explicit, not hidden in defaults",
          "Encryption: Encrypt sensitive data at rest and in transit",
        ],
      },
      reliability: {
        title: "Reliability Engineering Patterns",
        patterns: {
          "Circuit Breaker":
            "Trip after N consecutive failures, half-open recovery",
          "Retry with Intelligence":
            "Exponential backoff with jitter, retry budget",
          "Health Checks":
            "Test actual functionality, not just process existence",
          "Graceful Degradation":
            "Feature flags, cached responses, simplified UI",
        },
      },
    };

    return {
      content: [
        {
          type: "text",
          text: JSON.stringify(
            domain === "all" ? patterns : patterns[domain] || {},
            null,
            2
          ),
        },
      ],
    };
  }

  async getImplementationChecklist(phase) {
    const checklists = {
      pre_implementation: {
        title: "Pre-Implementation Checklist",
        understanding: [
          "Requirements clearly understood and documented",
          "Existing patterns analyzed and documented",
          "Similar implementations in codebase reviewed",
          "Error scenarios and edge cases identified",
          "Performance requirements established",
        ],
        design: [
          "Function design follows SRP (Single Responsibility Principle)",
          "Parameter count optimized (2-3 parameters preferred)",
          "Data structures selected based on access patterns",
          "Error handling strategy defined",
          "Testing approach planned",
        ],
      },
      implementation: {
        title: "Implementation Checklist",
        code_quality: [
          "Functions are 5-15 lines (max 20 lines)",
          "Maximum 3 levels of nesting",
          "Variable names are descriptive and clear",
          "Comments explain 'why' not 'what'",
          "No magic numbers or strings",
        ],
        error_handling: [
          "All external calls wrapped in proper error handling",
          "Structured error types used instead of strings",
          "User-friendly error messages implemented",
          "Logging implemented with appropriate detail levels",
          "Circuit breakers implemented for external dependencies",
        ],
        security: [
          "Input validation at system boundaries",
          "Whitelist approach for input validation",
          "Sensitive data properly encrypted",
          "Principle of least privilege applied",
          "No internal details exposed to users",
        ],
        performance: [
          "Profiling completed before optimization",
          "Caching strategy implemented where appropriate",
          "Resource pooling used for expensive operations",
          "Memory leaks prevented",
          "Algorithmic complexity considered",
        ],
        testing: [
          "Unit tests for business logic (70% of test suite)",
          "Integration tests for component interactions (20%)",
          "E2E tests for critical user journeys (10%)",
          "Error scenarios tested",
          "Performance benchmarks established",
        ],
      },
      post_implementation: {
        title: "Post-Implementation Checklist",
        quality_assurance: [
          "Code review completed with focus on patterns",
          "All tests passing (unit, integration, E2E)",
          "Performance benchmarks met",
          "Security review passed",
          "Documentation updated",
        ],
        monitoring: [
          "Health checks implemented",
          "Metrics collection configured",
          "Alerting set up for critical failures",
          "Logging structured and searchable",
          "Error tracking configured",
        ],
      },
    };

    return {
      content: [
        {
          type: "text",
          text: JSON.stringify(
            phase === "all" ? checklists : checklists[phase] || {},
            null,
            2
          ),
        },
      ],
    };
  }

  async run() {
    const transport = new StdioServerTransport();
    await this.server.connect(transport);
    console.error("Thinking Patterns MCP server running on stdio");
  }
}

// Start the server
const server = new ThinkingPatternsServer();
server.run().catch(console.error);
