import { lazy } from "react";
import Order from "../../views/order";
import Products from "../../views/order/Products";

const Bots = lazy(() => import("../../views/pages/bots"));
const DataSource = lazy(() => import("../../views/pages/datasource"));
const Hub = lazy(() => import("../../views/hub"));
const LeadCenter = lazy(() => import("../../views/pages/leadCenter"));
const Contact = lazy(() => import("../../views/contact"));

const AppRoutes = [
  {
    path: "/hub/chat",
    element: <Hub />,
    meta: {
      appLayout: true,
      className: "chat-application",
      menuCollapsed: true,
    },
  },
  {
    path: "/hub/contact",
    element: <Contact />,
    meta: {
      menuCollapsed: false,
    },
  },
  {
    path: "/lead-center",
    element: <LeadCenter />,
    meta: {
      className: "kanban-application",
    },
  },
  {
    path: "/apps/bot",
    element: <Bots />,
  },
  {
    path: "/apps/data-source",
    element: <DataSource />,
  },
  {
    path: "/order/list",
    element: <Order />,
  },
  {
    path: "/product",
    element: <Products />,
  },
];

export default AppRoutes;
