import { lazy } from "react";
import { protectRouterValidator } from "../../@core/components/permission-checker/ProtectedRoute";

const Users = lazy(() => import("../../views/setting/users"));
const Company = lazy(() => import("../../views/setting/company"));
const Teams = lazy(() => import("../../views/setting/teams"));
const Roles = lazy(() => import("../../views/setting/roles"));
const Pricing = lazy(() => import("../../views/pricing"));

console.log(protectRouterValidator("read_bot"));
const SettingRoutes = [
  {
    element: <Users />,
    path: "/setting/users",
    permission: "read_user",
  },
  {
    element: <Roles />,
    path: "/setting/roles",
  },
  {
    element: <Teams />,
    path: "/setting/teams",
  },
  {
    element: <Company />,
    path: "/setting/company",
  },
  {
    element: <Pricing />,
    path: "/pricing",
  },
];

export default SettingRoutes;
