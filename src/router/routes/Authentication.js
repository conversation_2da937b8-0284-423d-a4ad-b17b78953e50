// ** React Imports
import { lazy } from "react";

const ForgotPassword = lazy(() =>
  import("../../views/pages/authentication/ForgotPassword")
);
const ResetPassword = lazy(() =>
  import("../../views/pages/authentication/ResetPassword")
);
const VerifyEmail = lazy(() =>
  import("../../views/pages/authentication/VerifyEmail")
);

const AuthenticationRoutes = [
  {
    path: "/forgot-password",
    element: <ForgotPassword />,
    layout: "BlankLayout",
    meta: {
      layout: "blank",
      publicRoute: true,
      restricted: true,
    },
  },
  {
    path: "/reset-password",
    element: <ResetPassword />,
    meta: {
      layout: "blank",
      publicRoute: true,
      restricted: true,
    },
  },
  {
    path: "/verify-email",
    element: <VerifyEmail />,
    meta: {
      layout: "blank",
      publicRoute: true,
      restricted: true,
    },
  },
];

export default AuthenticationRoutes;
