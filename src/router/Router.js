import { lazy } from "react";
import { useRoutes, Navigate } from "react-router-dom";

import BlankLayout from "@layouts/BlankLayout";
import { useLayout } from "@hooks/useLayout";

import { getUserData } from "../utility/Utils";
import { getRoutes } from "./routes";
import TermsAndConditions from "../@core/layouts/TermsAndConditions";
import Register from "../views/pages/authentication/Register";
import KnowledgeBase from "../views/pages/knowledgeBase";
const Error = lazy(() => import("../views/pages/misc/Error"));
const Login = lazy(() => import("../views/pages/authentication/Login"));

const Router = () => {
  const { layout } = useLayout();

  const allRoutes = getRoutes(layout);
  const user = getUserData();
  console.log(user);

  const getHomeRoute = () => {
    if (user) {
      return "/hub/chat";
    } else {
      return "/login";
    }
  };

  const routes = useRoutes([
    {
      path: "/",
      index: true,
      element: <Navigate replace to={getHomeRoute()} />,
    },
    {
      path: "/login",
      element: user ? <Navigate to="/dashboard" /> : <BlankLayout />,
      children: [{ path: "/login", element: <Login /> }],
    },
    {
      path: "/register",
      element: user ? <Navigate to="/dashboard" /> : <BlankLayout />,
      children: [{ path: "/register", element: <Register /> }],
    },
    {
      path: "/terms-and-conditions",
      element: <TermsAndConditions />,
    },
    {
      path: "*",
      element: <BlankLayout />,
      children: [{ path: "*", element: <Error /> }],
    },
    {
      path: "/documentation",
      element: <KnowledgeBase />,
    },
    ...allRoutes,
  ]);

  return routes;
};

export default Router;
