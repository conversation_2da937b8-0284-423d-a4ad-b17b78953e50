import { put, get } from "./request";

export const create = async (customization) => {
  return put(`/bot-company/web/setting/web`, customization);
};

export const getCustomization = async (customization) => {
  return get(`/bot-company/web/setting/web`, customization);
};

export const updateCustomization = async (provider, botRequestId, data) => {
  return put(`/bot-company/${provider}/setting/${botRequestId}`, data);
};
