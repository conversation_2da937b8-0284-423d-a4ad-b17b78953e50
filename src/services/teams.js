import { del, get, post, put } from "./request";

export const getTeamsList = () => {
  return get("/team-management");
};

export const createTeam = (data) => {
  return post("/team-management", data);
};

export const deleteTeam = (id) => {
  return del(`/team-management/${id}`);
};

export const addEmployeeToTeam = (data) => {
  return post("/team-management/add-employee", data);
};

export const removeEmployeeFromTeam = (data) => {
  return del("/team-management/delete-employee", data);
};

export const updateTeam = (id, data) => {
  return put(`/team-management/${id}`, data);
};
