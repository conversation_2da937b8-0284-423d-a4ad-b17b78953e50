import { get } from "./request";

export const getFeelingByBotRequestId = async (botRequestId) => {
  return get(`/statistic/chat-feeling?botRequestId=${botRequestId}`);
};

export const getStatisticStatusByBotRequestId = async (botRequestId) => {
  const responseTimeAvg = await get(
    `/statistic/interaction-response-time-avg?botRequestId=${botRequestId}`
  );
  const chatLengthAvg = await get(
    `/statistic/interaction-avg?botRequestId=${botRequestId}`
  );
  const messagesLengthAvg = await get(
    `/statistic/messages-avg?botRequestId=${botRequestId}`
  );

  return {
    responseTimeAvg: responseTimeAvg.interactionsResponseTimeAvg,
    chatLengthAvg: chatLengthAvg.interactionAvg,
    messagesLengthAvg: messagesLengthAvg.messagesAvg,
  };
};

export const getInteractionsByBotRequestId = async (botRequestId) => {
  return get(`/statistic/interaction?botRequestId=${botRequestId}`);
};

export const getInteractionPeak = async (botRequestId) => {
  return get(`/statistic/interaction-peak?botRequestId=${botRequestId}`);
};
