import { post } from "./request";

export const subscribeToPlan = async (planIdentificationId) => {
  return post("/payment-gateway/subscribe", {
    idIdentify: planIdentificationId,
  });
};

export const markUserAsPaid = async (idIdentify) => {
  return post("/payment-gateway/mark-user-as-paid", {
    idIdentify,
  });
};

export const cancelSubscription = async (idIdentify) => {
  return post("/payment-gateway/cancel-subscription", {
    idIdentify,
  });
};
