import { del, get, post, put } from "./request";

export const getProducts = async () => {
  return get(`/product`);
};

export const getProduct = async (id) => {
  return get(`/product/${id}`);
};

export const deleteProduct = async (id) => {
  return del(`/product/${id}`);
};

export const updateProduct = async (id, data) => {
  return put(`/product/${id}`, data);
};

export const createProduct = async (data) => {
  return post(`/product`, data);
};
