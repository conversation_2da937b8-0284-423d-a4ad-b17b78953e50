import { del, get, post } from "./request";

export const deleteChatBySessionId = async (sessionId) => {
  console.log(sessionId);
  return del(`/chat-session/${sessionId}`);
};

export const getChatSessionList = async () => {
  return get("/chat-session");
};

export const toggleAi = async (sessionId) => {
  return post(`/chat-session/toggle-ai-agent/${sessionId}`);
};

export const assignChatToTeam = async (chatSessionId, teamId) => {
  return post(`/chat-session/assign-chat-to-team`, { chatSessionId, teamId });
};
