import { del, get, post, put } from "./request";

export const addSocialIntegration = (chatProvider, data) => {
  return post(`/social-auth/${chatProvider}/continue`, data);
};

export const loginWithGoogle = (botRequestId, userId) => {
  return post("/social-auth/google/auth", { botRequestId, userId });
};

export const getAllIntegration = () => {
  return get("/social-auth");
};

export const deleteIntegration = (id) => {
  return del(`/social-auth/integration/${id}`);
};

export const unlinkIntegration = (id) => {
  return put(`/social-auth/integration/${id}/unlink`);
};

export const getIntegrationById = (id) => {
  return get(`/social-auth/${id}`);
};

export const getIntegration = () => {
  return get(`/social-auth/botCompany`);
};

export const getIntegrationByBotRequestId = (botRequestId) => {
  return get(`/social-auth/integration/botCompany/${botRequestId}`);
};

export const updateSelectedSocialAuth = (data) => {
  return put("/social-auth/integration/select", data);
};

export const getGoogleAuthUrl = (botRequestId) => {
  return get(`/social-auth/google/${botRequestId}/auth-url`);
};
