import axios from "axios";
import { getCookie } from "../../utility/Utils";
import { ENV_SETTING } from "../../utility/constants";

const API_URL = ENV_SETTING.VITE_API_URL;

const axiosInstance = axios.create({
  baseURL: API_URL,
  headers: {
    "Content-Type": "application/json",
  },
});

axiosInstance.interceptors.request.use((config) => {
  const accessToken = sessionStorage.getItem("accessToken");

  if (accessToken) {
    config.headers["Authorization"] = `Bearer ${accessToken}`;
  } else {
    config.headers["Authorization"] = `Bearer ${getCookie("accessToken")}`;
  }

  return config;
});

export const get = async (path) => {
  return axiosInstance.get(`${API_URL}${path}`).then((res) => res.data);
};

export const post = async (path, data) => {
  return axiosInstance.post(`${API_URL}${path}`, data).then((res) => res.data);
};

export const put = async (path, data) => {
  return axiosInstance.put(`${API_URL}${path}`, data).then((res) => res.data);
};

export const putFile = async (path, formData) => {
  let accessToken = sessionStorage.getItem("accessToken");

  if (!accessToken) {
    accessToken = getCookie("accessToken");
  }

  return fetch(`${API_URL}${path}`, {
    method: "PUT",
    body: formData,
    headers: {
      Authorization: `Bearer ${accessToken}`,
    },
  });
};

export const del = async (path) => {
  return axiosInstance.delete(`${API_URL}${path}`).then((res) => res.data);
};

export const loginPost = async (data) =>
  axios.post(`${API_URL}/auth/login`, data);
