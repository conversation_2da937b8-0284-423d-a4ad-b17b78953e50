import { del, get, post, put } from "./request";

export const getAllEmployees = () => {
  return get(`/employee`);
};

export const deleteEmployee = (id) => {
  return del(`/employee/${id}`);
};

export const createEmployee = async (data) => {
  return post(`/employee`, data);
};

export const updateCompanyInfo = async (data) => {
  return put(`/companies`, data);
};

export const getCompanyInfo = async () => {
  return get(`/companies`);
};

export const updateProvider = async (provider, data) => {
  return put(`/companies/provider/${provider}`, data);
};

export const getProviderInfo = async (provider) => {
  return get(`/companies/provider/${provider}`);
};

export const updateEmployee = async (id, data) => {
  return put(`/employee/${id}`, data);
};
