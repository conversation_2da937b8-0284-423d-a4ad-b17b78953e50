import { loginPost, post, put } from "./request";

export const login = async (email, password) => {
  return loginPost({ email, password }).then((res) => res.data);
};

export const signup = async (email, password, name) => {
  return post(`/auth/signup`, { email, password, name });
};

export const resetPassword = (email) => {
  return post(`/auth/recovery/password`, { email });
};

export const updatePassword = (password, token) => {
  return put(`/auth/change/password`, { password, token });
};

export const resendVerificationEmail = (email) => {
  return post(`/auth/send/verify-email`, { email });
};
