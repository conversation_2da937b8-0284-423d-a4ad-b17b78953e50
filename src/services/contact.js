import { del, get, post, put } from "./request";

export const getAllContacts = async () => {
  return get("/contact");
};

export const createContact = async (contact) => {
  return post("/contact", contact);
};

export const deleteContact = async (id) => {
  return del(`/contact/${id}`);
};

export const toggleAIContact = async (id) => {
  return put(`/contact/${id}/toggle-ai`);
};

export const createContactFromChatSession = async (contact) => {
  return post("/contact/create-from-chat-session", contact);
};
