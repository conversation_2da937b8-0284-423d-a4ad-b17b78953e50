import { get, post, del, put } from "./request";

export const getOrders = async () => {
  return get(`/order`);
};

export const getOrder = async (orderId) => {
  return get(`/order/${orderId}`);
};

export const createOrder = async (data) => {
  return post(`/order`, data);
};

export const deleteOrder = async (id) => {
  return del(`/order/${id}`);
};

export const updateOrderStatus = async (id, status) => {
  return put(`/order/status/${id}`, { status });
};

export const updateOrder = async (orderData) => {
  return put('/order', orderData);
};
