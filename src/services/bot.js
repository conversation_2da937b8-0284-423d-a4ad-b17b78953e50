import { get, post, del, put, putFile } from "./request";

export const createBot = async (bot) => {
  return post(`/bot-company/create`, bot);
};

export const getWebBot = async (provider = "web") => {
  return get(`/bot-company/${provider}`);
};

export const getAllBots = async () => {
  return get(`/bot-company`);
};

export const deleteBot = async (botId) => {
  return del(`/bot-company/${botId}`);
};

export const updateBot = async (botRequestId, provider, bot) => {
  return put(`/bot-company/${provider}/setting/${botRequestId}`, bot);
};

export const updateBotIcon = async (provider, data) => {
  return putFile(`/bot-company/${provider}/icon`, data);
};

export const toggleOrderIntegration = async (botRequestId, orderEnabled) => {
  return put(`/bot-company/${botRequestId}/order`, { orderEnabled });
};
