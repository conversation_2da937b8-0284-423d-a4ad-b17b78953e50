import { del, get, post, put } from "./request";

export const getRoleList = async () => {
  const roles = await get("/roles");
  const permission = await get("/permissions");
  return {
    roles,
    permission,
  };
};

export const getRoleById = (id) => {
  return get(`/roles/${id}`);
};

export const deleteRole = (id) => {
  return del(`/roles/${id}`);
};

export const createRole = (data) => {
  return post("/roles", data);
};

export const updateRole = (id, data) => {
  return put(`/roles/${id}`, data);
};

export const assignPermissionToRole = (data) => {
  return post(`/roles/assign-permission`, data);
};
