import { useState, useEffect, Fragment } from "react";
import { useLocation } from "react-router-dom";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON>Header,
} from "reactstrap";
import withReactContent from "sweetalert2-react-content";
import Swal from "sweetalert2";

import { pricingData } from "./pricing-data";
import PricingCards from "./PricingCards";
import PricingTrial from "./PricingTrial";
import PricingHeader from "./PricingHeader";
import { cancelSubscription, markUserAsPaid } from "../../services/payment";
import { getLoginInfo, getUserData, setCookie } from "../../utility/Utils";

import "@styles/base/pages/page-pricing.scss";
import { useTranslation } from "react-i18next";
import toast from "react-hot-toast";

const Pricing = () => {
  const NotificationSwal = withReactContent(Swal);
  const location = useLocation();
  const params = new URLSearchParams(location.search);
  const userData = getUserData();
  const { t } = useTranslation();

  const [data, setData] = useState(null),
    [faq, setFaq] = useState(null),
    [duration, setDuration] = useState("monthly");
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [showErrorModal, setShowErrorModal] = useState(false);
  const [currentSubscription, setCurrentSubscription] = useState(null);

  const handleSuccessSubscribe = async () => {
    await markUserAsPaid(params.get("idIdentify"));
    setCurrentSubscription(params.get("idIdentify"));

    if (userData.rememberMe == "true") {
      setCookie(
        "userData",
        JSON.stringify({
          ...userData,
          subscription: {
            ...userData.subscription,
            idIdentify: params.get("idIdentify"),
          },
        }),
        10
      );
    } else {
      sessionStorage.setItem(
        "userData",
        JSON.stringify({
          ...userData,
          subscription: {
            ...userData.subscription,
            idIdentify: params.get("idIdentify"),
          },
        })
      );
    }
  };

  const handleSubscriptionCancel = async () => {
    window.location.href =
      "https://billing.stripe.com/p/login/test_fZe02xfk53mC2cw000";
    // NotificationSwal.fire({
    //   title: "¿Deseas cancelar tu suscripción?",
    //   text: "Aun puedes seguir usando tu bot hasta la fecha de expiración.",
    //   icon: "warning",
    //   showCancelButton: true,
    //   confirmButtonText: t("general.yesDelete"),
    //   customClass: {
    //     confirmButton: "btn btn-primary",
    //     cancelButton: "btn btn-outline-danger ms-1",
    //   },
    //   buttonsStyling: false,
    // }).then(async (result) => {
    //   if (result.value) {
    //     const response = await cancelSubscription(currentSubscription).catch(
    //       (err) => err
    //     );
    //     if (response?.canceled) {
    //       setCurrentSubscription(null);
    //       delete userData.subscription;

    //       if (userData.rememberMe == "true") {
    //         setCookie("userData", JSON.stringify({ ...userData }), 10);
    //       } else {
    //         sessionStorage.setItem("userData", JSON.stringify({ ...userData }));
    //       }

    //       toast.success("Tu suscripción ha sido cancelada");
    //     } else {
    //       toast.error("No se pudo cancelar la suscripción");
    //     }
    //   }
    // });
  };

  useEffect(() => {
    const dataArr = [],
      faqArr = [];

    Object.entries(pricingData.pricing).forEach(([key, val]) => {
      if (key !== "qandA") {
        dataArr.push(val);
        setData([...dataArr]);
      } else {
        faqArr.push(val);
        setFaq(faqArr[0]);
      }
    });

    if (params.get("success") == "true") {
      setShowSuccessModal(true);
      handleSuccessSubscribe();
    } else if (params.get("success") == "false") {
      setShowErrorModal(true);
    }

    if (userData?.subscription?.idIdentify) {
      setCurrentSubscription(userData?.subscription?.idIdentify);
    }
  }, []);

  return (
    <div id="pricing-table">
      <PricingHeader duration={duration} setDuration={setDuration} />
      {data !== null && faq !== null ? (
        <Fragment>
          <PricingCards
            data={data}
            duration={duration}
            currentSubscription={currentSubscription}
          />
          <PricingTrial />
          {currentSubscription && (
            <div className="d-flex justify-content-center align-items-center p-5">
              <Button
                color="danger"
                onClick={handleSubscriptionCancel}
                size="lg"
              >
                Cancelar suscripción
              </Button>
            </div>
          )}
        </Fragment>
      ) : null}
      <Modal
        isOpen={showSuccessModal}
        modalClassName="modal-success"
        className="modal-dialog-centered"
      >
        <ModalHeader>Payment success</ModalHeader>
        <ModalBody>
          <p>Pago procesado exitosamente, Bienvenido a Biitbot.</p>
        </ModalBody>
        <ModalFooter>
          <Button color="success" onClick={() => setShowSuccessModal(false)}>
            OK
          </Button>
        </ModalFooter>
      </Modal>
      <Modal
        modalClassName="modal-danger"
        className="modal-dialog-centered"
        isOpen={showErrorModal}
        toggle={() => setShowErrorModal(!showErrorModal)}
      >
        <ModalHeader>Payment error</ModalHeader>
        <ModalBody>
          <p>Error al procesar el pago, porfavor intentelo mas tarde</p>
        </ModalBody>
        <ModalFooter>
          <Button color="danger" onClick={() => setShowErrorModal(false)}>
            OK
          </Button>
        </ModalFooter>
      </Modal>
    </div>
  );
};

export default Pricing;
