// ** Reactstrap Imports
import { Row, Col, Button } from "reactstrap";

// ** Images
import trialSvg from "@src/assets/images/illustration/pricing-Illustration.svg";

const PricingTrial = () => {
  return (
    <div className="pricing-free-trial">
      <Row>
        <Col className="mx-auto" lg={{ size: 10, offset: 3 }} sm="12">
          <div className="pricing-trial-content d-flex justify-content-between">
            <div className="text-center text-md-start mt-3">
              <h3 className="text-primary">
                Aun no estas convencido? Empieza con una prueba de 30-días
                GRATIS!
              </h3>
              <h5>
                Obtén una prueba de 30-días gratis para ver si tus necesidades
                de marketing son compatibles con Biitbot.
              </h5>
              <div className="mt-2 mt-lg-3">
                <Button color="primary">
                  Comenzar la prueba gratuita de 30-días
                </Button>
              </div>
            </div>
            <img
              className="pricing-trial-img img-fluid"
              src={trialSvg}
              alt="trial svg"
              style={{
                transform: "scaleX(1)",
              }}
            />
          </div>
        </Col>
      </Row>
    </div>
  );
};

export default PricingTrial;
