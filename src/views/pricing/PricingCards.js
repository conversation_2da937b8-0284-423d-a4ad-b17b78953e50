import classnames from "classnames";
import {
  Row,
  Col,
  Card,
  CardBody,
  CardText,
  Badge,
  ListGroup,
  ListGroupItem,
  Button,
  Alert,
} from "reactstrap";

import { subscribeToPlan } from "../../services/payment";

const PricingCards = ({
  data,
  duration,
  bordered,
  fullWidth,
  cols,
  currentSubscription,
}) => {
  const colsProps = cols ? cols : { md: 4, xs: 12 };

  const handleSubscribe = async (idIdentify) => {
    const response = await subscribeToPlan(idIdentify);

    if (response?.redirect) {
      window.location.href = response.redirect;
    }
  };

  const renderPricingCards = () => {
    return data.map((item, index) => {
      const monthlyPrice =
          duration === "yearly" ? item.yearlyPlan.perMonth : item.monthlyPrice,
        yearlyPrice =
          duration === "yearly"
            ? item.yearlyPlan.totalAnnual
            : item.monthlyPrice;

      return (
        <Col key={index} {...colsProps}>
          <Card
            className={classnames("text-center", {
              border: bordered,
              "shadow-none": bordered,
              popular: item.popular === true && !currentSubscription,
              active: item.planIdentifyId === currentSubscription,
            })}
          >
            <CardBody>
              {item.popular === true ? (
                <div className="pricing-badge text-center mb-1">
                  <Badge color="light-primary p-1" pill>
                    Popular
                  </Badge>
                </div>
              ) : null}
              <h3>{item.title}</h3>
              <CardText>{item.subtitle}</CardText>
              <div className="annual-plan">
                <div className="plan-price mt-2">
                  <sup className="font-medium-1 fw-bold text-primary me-25">
                    $
                  </sup>
                  <span
                    className={`pricing-${item.title.toLowerCase()}-value fw-bolder text-primary`}
                  >
                    {monthlyPrice}
                  </span>
                  <span className="pricing-duration text-body font-medium-1 fw-bold ms-25">
                    /month
                  </span>
                </div>
                {item.title !== "Basic" && duration === "yearly" ? (
                  <small className="annual-pricing text-muted">
                    USD {yearlyPrice} / year
                  </small>
                ) : null}
              </div>
              <ListGroup tag="ul" className="list-group-circle text-start mb-2">
                {item.planBenefits.map((benefit, i) => (
                  <ListGroupItem key={i} tag="li">
                    {benefit}
                  </ListGroupItem>
                ))}
              </ListGroup>
              <Button
                block
                disabled={item.planIdentifyId === currentSubscription}
                outline={item.planIdentifyId !== currentSubscription}
                color={
                  item.planIdentifyId === currentSubscription
                    ? "success"
                    : "primary"
                }
                onClick={() => handleSubscribe(item.planIdentifyId)}
              >
                {item.planIdentifyId === currentSubscription
                  ? "Plan activo actualmente"
                  : "Suscribirse"}
              </Button>
              {item.planIdentifyId !== currentSubscription && (
                <Alert color="warning" className="mt-2 p-1">
                  30 días prueba gratis.
                </Alert>
              )}
            </CardBody>
          </Card>
        </Col>
      );
    });
  };

  const defaultCols = {
    sm: { offset: 2, size: 10 },
    lg: { offset: 2, size: 10 },
  };

  return (
    <Row className="pricing-card">
      <Col
        {...(!fullWidth ? defaultCols : {})}
        className={classnames({ "mx-auto": !fullWidth })}
      >
        <Row>{renderPricingCards()}</Row>
      </Col>
    </Row>
  );
};

export default PricingCards;
