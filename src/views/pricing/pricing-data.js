import Pot1 from "@src/assets/images/illustration/Pot1.svg";
import Pot2 from "@src/assets/images/illustration/Pot2.svg";
import Pot3 from "@src/assets/images/illustration/Pot3.svg";

export const pricingData = {
  pricing: {
    basicPlan: {
      title: "Básico",
      subtitle: "Soluciones para trabajadores independientes",
      monthlyPrice: 25,
      planIdentifyId: "price_1PTuE5Bv9J2WILewrzCDI2TF",
      paymentLink: "https://buy.stripe.com/test_6oE01gbdf7ejeJOfYZ",
      yearlyPlan: {
        perMonth: 18,
        totalAnnual: 216,
      },
      planBenefits: [
        "1 chatbot para paginas web",
        "1 chatbot para WhatsApp",
        "5 usuarios con acceso a la plataforma",
        "2,000 Mensajes/Mes generados por la AI",
        "Soporte 24/7 con nuestra AI experta",
      ],
      popular: false,
    },
    standardPlan: {
      title: "Est<PERSON><PERSON>",
      subtitle: "Soluciones para pequeños negocios",
      monthlyPrice: 50,
      planIdentifyId: "price_1PTuEPBv9J2WILewOmN8aqEo",
      paymentLink: "https://buy.stripe.com/test_9AQ15kepr2Y345a3ce",
      yearlyPlan: {
        perMonth: 40,
        totalAnnual: 444,
      },
      planBenefits: [
        "2 chatbot para paginas web",
        "2 chatbot para WhatsApp",
        "8 usuarios con acceso a la plataforma",
        "4,000 Mensajes/Mes generados por la AI",
        "Centro de cliente potenciales asistido por AI",
        "Soporte 24/7 con nuestra AI experta",
        "Soporte personalizado",
      ],
      popular: true,
    },
    enterprisePlan: {
      title: "Premium",
      subtitle: "Soluciones para grandes negocios",
      monthlyPrice: 99,
      planIdentifyId: "price_1PTuEeBv9J2WILew1pxbjG0U",
      paymentLink: "https://buy.stripe.com/test_14k15k9571TZdFK28b",
      yearlyPlan: {
        perMonth: 80,
        totalAnnual: 960,
      },
      planBenefits: [
        "4 chatbot para paginas web",
        "4 chatbot para WhatsApp",
        "1 chatbot para Instagram",
        "1 chatbot para Facebook Messenger",
        "12 usuarios con acceso a la plataforma",
        "6,000 Mensajes/Mes generados por la AI",
        "Centro de cliente potenciales asistido por AI",
        "Soporte 24/7 con nuestra AI experta",
        "Soporte personalizado",
        "Agente de cuenta para soporte personalizado",
      ],
      popular: false,
    },
    qandA: [
      {
        question: "Does my subscription automatically renew?",
        ans: "Pastry pudding cookie toffee bonbon jujubes jujubes powder topping. Jelly beans gummi bears sweet roll bonbon muffin liquorice. Wafer lollipop sesame snaps. Brownie macaroon cookie muffin cupcake candy caramels tiramisu. Oat cake chocolate cake sweet jelly-o brownie biscuit marzipan. Jujubes donut marzipan chocolate bar. Jujubes sugar plum jelly beans tiramisu icing cheesecake.",
      },
      {
        question: "Can I store the item on an intranet so everyone has access?",
        ans: "Tiramisu marshmallow dessert halvah bonbon cake gingerbread. Jelly beans chocolate pie powder. Dessert pudding chocolate cake bonbon bear claw cotton candy cheesecake. Biscuit fruitcake macaroon carrot cake. Chocolate cake bear claw muffin chupa chups pudding.",
      },
      {
        question: "Am I allowed to modify the item that I purchased?",
        ans: "Tart gummies dragée lollipop fruitcake pastry oat cake. Cookie jelly jelly macaroon icing jelly beans soufflé cake sweet. Macaroon sesame snaps cheesecake tart cake sugar plum. Dessert jelly-o sweet muffin chocolate candy pie tootsie roll marzipan. Carrot cake marshmallow pastry. Bonbon biscuit pastry topping toffee dessert gummies. Topping apple pie pie croissant cotton candy dessert tiramisu.",
      },
    ],
  },
};
