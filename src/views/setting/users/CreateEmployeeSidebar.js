import { useState, useEffect } from "react";
import { Button, Label, Form, Input } from "reactstrap";

import Sidebar from "@components/sidebar";
import { useTranslation } from "react-i18next";

const CreateEmployeeSidebar = ({
  open,
  toggleSidebar,
  onCreate,
  defaultValue,
  onEdit,
}) => {
  const [fullName, setFullName] = useState();
  const [email, setEmail] = useState();
  const [password, setPassword] = useState();

  const { t } = useTranslation();

  const isEditing = defaultValue && Object.keys(defaultValue).length > 0;

  const onSubmit = async (e) => {
    e.preventDefault();
    if (isEditing) {
      await onEdit(defaultValue?.id, {
        name: fullName,
        email,
        password,
      });
    } else {
      await onCreate({
        name: fullName,
        email,
        password,
      });
    }
  };

  const handleSidebarClosed = () => {};

  const handleToggleSidebar = () => {
    toggleSidebar();
    setFullName("");
    setEmail("");
    setPassword("");
  };

  const handleOnChange = (e) => {
    const { name, value } = e.target;
    if (name === "fullName") {
      setFullName(value);
    } else if (name === "email") {
      setEmail(value);
    } else if (name === "password" && value.length > 0) {
      setPassword(value);
    }
  };

  useEffect(() => {
    if (defaultValue?.name) {
      setFullName(defaultValue.name);
    }

    if (defaultValue?.email) {
      setEmail(defaultValue.email);
    }

    if (defaultValue?.password) {
      setPassword(defaultValue?.password);
    }
  }, [defaultValue]);

  return (
    <Sidebar
      size="lg"
      open={open}
      title={t("user.sidebar.title")}
      headerClassName="mb-1"
      contentClassName="pt-0"
      toggleSidebar={handleToggleSidebar}
      onClosed={handleSidebarClosed}
    >
      <Form onSubmit={onSubmit}>
        <div className="mb-1">
          <Label className="form-label" for="fullName">
            {t("general.fullName")} <span className="text-danger">*</span>
          </Label>
          <Input
            id="fullName"
            required
            placeholder={t("general.fullName")}
            value={fullName}
            name="fullName"
            onChange={handleOnChange}
          />
        </div>
        <div className="mb-1">
          <Label className="form-label" for="userEmail">
            {t("general.email")} <span className="text-danger">*</span>
          </Label>
          <Input
            type="email"
            id="userEmail"
            placeholder={t("general.email")}
            required
            name="email"
            value={email}
            onChange={handleOnChange}
          />
        </div>
        <div className="mb-1">
          <Label className="form-label" for="password">
            {t("general.password")} <span className="text-danger">*</span>
          </Label>
          {isEditing ? (
            <Input
              id="password"
              placeholder="******"
              type="password"
              name="password"
              value={password}
              onChange={handleOnChange}
            />
          ) : (
            <Input
              id="password"
              placeholder="******"
              type="password"
              name="password"
              required
              value={password}
              onChange={handleOnChange}
            />
          )}
        </div>
        <Button type="submit" className="me-1" color="primary">
          {isEditing ? t("general.update") : t("general.create")}
        </Button>
        <Button type="reset" color="secondary" outline onClick={toggleSidebar}>
          {t("general.cancel")}
        </Button>
      </Form>
    </Sidebar>
  );
};

export default CreateEmployeeSidebar;
