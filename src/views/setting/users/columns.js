import React from "react";
import { Edit, MoreVertical, Trash2 } from "react-feather";
import {
  UncontrolledDropdown,
  DropdownToggle,
  DropdownMenu,
  DropdownItem,
} from "reactstrap";
import moment from "moment";
import { useTranslation } from "react-i18next";
import { getUserData } from "../../../auth/utils";

import Avatar from "@components/avatar";

const renderClient = (fullName) => {
  return (
    <Avatar
      initials
      className="me-1"
      color={"light-primary"}
      content={fullName || "None"}
    />
  );
};

export const columns = (onDelete, onEdit) => {
  const { t } = useTranslation();

  const currentUser = getUserData();

  return [
    {
      name: t("user.column.fullNameColumn"),
      sortable: false,
      minWidth: "300px",
      selector: (row) => row.name,
      cell: (row) => (
        <div className="d-flex justify-content-left align-items-center">
          {renderClient(row.name)}
          <div className="d-flex flex-column">
            <span className="fw-bolder text-uppercase ">{row.name}</span>
            <small className="text-truncate text-muted mb-0 text-uppercase">
              {row.email}
            </small>
          </div>
        </div>
      ),
    },
    {
      name: t("general.confirmed"),
      minWidth: "138px",
      sortable: false,
      selector: (row) => row.shouldUpdatePassword,
      cell: (row) => (
        <span className="text-uppercase">
          {row?.shouldUpdatePassword
            ? t("general.confirmed")
            : t("general.notConfirmed")}
        </span>
      ),
    },
    {
      name: t("user.column.creationDateColumn"),
      minWidth: "230px",
      sortable: true,
      sortField: "created_at",
      selector: (row) => row.created_at,
      cell: (row) => (
        <span className="text-capitalize">
          {moment(row.created_at).format("DD/MM/YYYY")}
        </span>
      ),
    },
    {
      name: t("user.column.actionsColumn"),
      minWidth: "100px",
      cell: (row) => (
        <div className="column-action">
          <UncontrolledDropdown>
            <DropdownToggle tag="div" className="btn btn-sm">
              <MoreVertical size={14} className="cursor-pointer" />
            </DropdownToggle>
            <DropdownMenu>
              {currentUser.email !== row.email && (
                <DropdownItem
                  tag="a"
                  href="/"
                  className="w-100"
                  onClick={(e) => {
                    e.preventDefault();
                    onDelete(row.id);
                  }}
                >
                  <Trash2 size={14} className="me-50" />
                  <span className="align-middle">{t("general.delete")}</span>
                </DropdownItem>
              )}
              <DropdownItem
                tag="a"
                href="/"
                className="w-100"
                onClick={(e) => {
                  e.preventDefault();
                  onEdit(row.id, row);
                }}
              >
                <Edit size={14} className="me-50" />
                <span className="align-middle">{t("general.edit")}</span>
              </DropdownItem>
            </DropdownMenu>
          </UncontrolledDropdown>
        </div>
      ),
    },
  ];
};
