import { Fragment, useState } from "react";
import { <PERSON><PERSON>, <PERSON> } from "reactstrap";
import DataTable from "react-data-table-component";
import { useTranslation } from "react-i18next";

import CreateEmployeeSidebar from "./CreateEmployeeSidebar";
import { columns } from "./columns";

import "@styles/react/libs/react-select/_react-select.scss";
import "@styles/react/libs/tables/react-dataTable-component.scss";
import EmptyState from "../../../@core/components/empty-state/empty-state-table";

const EmployeeList = ({ data, onCreate, onDelete, onEdit }) => {
  const { t } = useTranslation();
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [selectedRow, setSelectedRow] = useState(null);

  const toggleSidebar = () => {
    setSidebarOpen(!sidebarOpen);
  };

  const handleCreateEmployee = async (data) => {
    return onCreate(data, toggleSidebar);
  };

  const handleOnDelete = async (id) => {
    await onDelete(id);
  };

  const handleOnEdit = (employeeId, employee) => {
    setSelectedRow(employee);
    toggleSidebar();
  };

  return (
    <Fragment>
      <Card>
        <div className="react-dataTable">
          <DataTable
            subHeader
            pagination
            responsive
            noDataComponent={
              <EmptyState
                title={t("general.dataNotFound")}
                message={t("user.emptyDescription")}
              />
            }
            columns={columns(handleOnDelete, handleOnEdit, t)}
            className="react-dataTable overflow-visible"
            data={data}
            subHeaderComponent={
              <div className="d-flex justify-content-between">
                <div className="position-relative">
                  <Button onClick={toggleSidebar} color="primary">
                    {t("user.addUser")}
                  </Button>
                </div>
              </div>
            }
          />
        </div>
      </Card>

      <CreateEmployeeSidebar
        open={sidebarOpen}
        toggleSidebar={toggleSidebar}
        onCreate={handleCreateEmployee}
        onEdit={onEdit}
        defaultValue={selectedRow}
      />
    </Fragment>
  );
};

export default EmployeeList;
