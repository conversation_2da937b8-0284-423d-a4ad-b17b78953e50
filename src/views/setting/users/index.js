import { useEffect, useState } from "react";
import { toast } from "react-hot-toast";
import { useTranslation } from "react-i18next"; // Asegúrate de tener react-i18next instalado

import EmployeeList from "./EmployeeList";
import {
  getAllEmployees,
  createEmployee,
  deleteEmployee,
  updateEmployee,
} from "../../../services/company";

import "@styles/react/apps/app-users.scss";

const Users = () => {
  const [employees, setEmployees] = useState([]);
  const { t } = useTranslation();

  const getEmployees = async () => {
    const response = await getAllEmployees();
    setEmployees(response);
  };

  const handleDelete = async (id) => {
    deleteEmployee(id)
      .then(() => {
        toast.success(t("user.deleteSuccess"));
      })
      .catch((err) => {
        if (err.response.data.message === "cannot delete the only admin") {
          toast.error(t("user.deleteErrorAdmin"));
        } else {
          toast.error(t("user.deleteError"));
        }
      });

    getEmployees();
  };

  const handleCreate = async (data, callback) => {
    const response = await createEmployee(data);

    if (!response.code) {
      getEmployees();
      toast.success(t("user.createSuccess"));
      callback();
    } else {
      if (response.response.data.message === "user already exist") {
        return toast.error(t("user.createErrorExist"));
      }

      toast.error(t("user.createError"));
    }
  };

  const handleEdit = async (employeeId, data) => {
    updateEmployee(employeeId, data)
      .then(() => {
        toast.success(t("user.editSuccess"));
        getEmployees();
      })
      .catch(() => {
        toast.error(t("user.editError"));
      });
  };

  useEffect(() => {
    getEmployees();
  }, []);

  return (
    <div className="app-user-list">
      <EmployeeList
        data={employees}
        onCreate={handleCreate}
        onDelete={handleDelete}
        onEdit={handleEdit}
      />
    </div>
  );
};

export default Users;
