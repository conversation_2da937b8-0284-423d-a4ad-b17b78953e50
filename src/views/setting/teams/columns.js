import { Edit, MoreVertical, Plus, Trash2 } from "react-feather";
import {
  UncontrolledDropdown,
  DropdownToggle,
  DropdownMenu,
  DropdownItem,
} from "reactstrap";
import moment from "moment";

import Avatar from "@components/avatar";

const renderClient = (fullName) => {
  return (
    <Avatar
      initials
      className="me-1"
      color={"light-primary"}
      content={fullName || "None"}
    />
  );
};

export const columns = (onDelete, onAddUser, onEdit, t) => [
  {
    name: t("teams.table.column.teamName"),
    sortable: false,
    minWidth: "300px",
    selector: (row) => row.name,
    cell: (row) => (
      <div className="d-flex justify-content-left align-items-center">
        {renderClient(row.name)}
        <div className="d-flex flex-column">
          <span className="fw-bolder text-uppercase ">{row.name}</span>
          <small className="text-truncate text-muted mb-0 text-uppercase">
            {row.email}
          </small>
        </div>
      </div>
    ),
  },
  {
    name: t("teams.table.column.creationDate"),
    minWidth: "230px",
    sortable: true,
    sortField: "created_at",
    selector: (row) => row.created_at,
    cell: (row) => (
      <span className="text-capitalize">
        {moment(row.created_at).format("DD/MM/YYYY")}
      </span>
    ),
  },
  {
    name: t("general.actions"),
    minWidth: "100px",
    cell: (row) => (
      <div className="column-action">
        <UncontrolledDropdown>
          <DropdownToggle tag="div" className="btn btn-sm">
            <MoreVertical size={14} className="cursor-pointer" />
          </DropdownToggle>
          <DropdownMenu>
            <DropdownItem
              tag="a"
              href="/"
              className="w-100"
              onClick={(e) => {
                e.preventDefault();
                onDelete(row.id);
              }}
            >
              <Trash2 size={14} className="me-50" />
              <span className="align-middle">{t("general.delete")}</span>
            </DropdownItem>
            <DropdownItem
              tag="button"
              className="w-100"
              onClick={() => onEdit(row)}
            >
              <Edit size={14} className="me-50" />
              <span className="align-middle">{t("general.edit")}</span>
            </DropdownItem>
            <DropdownItem
              tag="button"
              className="w-100"
              onClick={() => onAddUser(row.id)}
            >
              <Plus size={14} className="me-50" />
              <span className="align-middle">
                {t("teams.table.column.addUser")}
              </span>
            </DropdownItem>
          </DropdownMenu>
        </UncontrolledDropdown>
      </div>
    ),
  },
];
