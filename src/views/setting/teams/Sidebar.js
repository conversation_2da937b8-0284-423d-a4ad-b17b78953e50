import { useEffect, useState } from "react";
import { Button, Label, Form, Input } from "reactstrap";
import { useTranslation } from "react-i18next";

import Sidebar from "@components/sidebar";

const SidebarNewUsers = ({
  open,
  toggleSidebar,
  onCreate,
  defaultValue = {},
  onEdit,
}) => {
  const { t } = useTranslation();
  const isEditing = Object.keys(defaultValue)?.length > 0 ? true : false;

  const [name, setName] = useState("");
  const [teamType, setTeamType] = useState();

  const onSubmit = async (e) => {
    e.preventDefault();
    if (isEditing) {
      return onEdit(defaultValue?.id, {
        name: name,
        isDefault: teamType === "isDefault" ? true : false,
        isSales: teamType === "isSales" ? true : false,
      });
    }
    return onCreate({
      name: name,
      isDefault: teamType === "isDefault" ? true : false,
      isSales: teamType === "isSales" ? true : false,
    });
  };

  const handleSidebarClosed = () => {};

  const handleToggleSidebar = () => {
    toggleSidebar();
    setName("");
    setTeamType();
  };

  useEffect(() => {
    return () => {
      setName("");
      setTeamType();
    };
  }, []);

  useEffect(() => {
    if (defaultValue?.name) {
      setName(defaultValue.name);
    }

    if (defaultValue?.isDefault) {
      setTeamType("isDefault");
    }

    if (defaultValue?.isSales) {
      setTeamType("isSales");
    }
  }, [defaultValue]);

  return (
    <Sidebar
      size="45"
      open={open}
      title={t("teams.sidebarNewUsers.title")}
      headerClassName="mb-1"
      contentClassName="pt-0"
      toggleSidebar={handleToggleSidebar}
      onClosed={handleSidebarClosed}
    >
      <Form onSubmit={onSubmit}>
        <div className="mb-1">
          <Label className="form-label" for="fullName">
            {t("teams.sidebarNewUsers.teamNameRequired")}
          </Label>
          <Input
            id="name"
            required
            placeholder={t("teams.sidebarNewUsers.teamNamePlaceholder")}
            value={name}
            name="name"
            onChange={(e) => setName(e.target.value)}
          />
        </div>
        <div className="mb-1 d-flex gap-1 align-items-center">
          <Label className="form-label mb-0 w-50" for="isDefault">
            {t("teams.sidebarNewUsers.defaultTeamLabel")}
          </Label>
          <Input
            id="isDefault"
            type="radio"
            name="isDefault"
            checked={teamType === "isDefault" ? true : false}
            onChange={() => setTeamType("isDefault")}
          />
        </div>
        <div className="mb-1 d-flex gap-1 align-items-center">
          <Label className="form-label mb-0 w-50" for="isSales">
            {t("teams.sidebarNewUsers.defaultSalesTeamLabel")}
          </Label>
          <Input
            id="isSales"
            type="radio"
            name="isSales"
            checked={teamType === "isSales" ? true : false}
            onChange={() => setTeamType("isSales")}
          />
        </div>
        <Button type="submit" className="me-1" color="primary">
          {isEditing
            ? t("teams.sidebarNewUsers.editButton")
            : t("teams.sidebarNewUsers.createButton")}
        </Button>
        <Button type="reset" color="secondary" outline onClick={toggleSidebar}>
          {t("teams.sidebarNewUsers.cancelButton")}
        </Button>
      </Form>
    </Sidebar>
  );
};

export default SidebarNewUsers;
