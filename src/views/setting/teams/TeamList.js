import { Fragment, useState } from "react";
import { <PERSON><PERSON>, <PERSON> } from "reactstrap";
import DataTable from "react-data-table-component";
import { useTranslation } from "react-i18next";

import Sidebar from "./Sidebar";
import { columns } from "./columns";
import EmptyState from "../../../@core/components/empty-state/empty-state-table";
import AddUserModal from "./addUserModal";

import "@styles/react/libs/react-select/_react-select.scss";
import "@styles/react/libs/tables/react-dataTable-component.scss";

const TeamList = ({ data, onCreate, onDelete, dispatchUpdate, onEdit }) => {
  const { t } = useTranslation();
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [showAddUserModal, setShowAddUserModal] = useState(false);
  const [teamSelected, setTeamSelected] = useState();

  const toggleSidebar = () => {
    setSidebarOpen(!sidebarOpen);
    setTeamSelected();
  };

  const handleCreateEmployee = async (data) => {
    try {
      await onCreate(data, toggleSidebar);
    } catch (error) {
      return;
    }
  };

  const handleOnDelete = async (id) => {
    await onDelete(id);
  };

  const handleOnEdit = async (teamId, team) => {
    await onEdit(teamId, team);
  };

  const openEditModal = async (id) => {
    setTeamSelected(id);
    setSidebarOpen(true);
  };

  const openAddUserModal = (id) => {
    setTeamSelected(id);
    setShowAddUserModal(!showAddUserModal);
  };

  return (
    <Fragment>
      <Card>
        <div className="react-dataTable">
          <DataTable
            subHeader
            pagination
            responsive
            noDataComponent={
              <EmptyState
                title={t("teams.table.noDataTitle")}
                message={t("teams.table.noDataMessage")}
              />
            }
            columns={columns(
              handleOnDelete,
              openAddUserModal,
              openEditModal,
              t
            )}
            className="react-dataTable overflow-visible"
            data={data}
            subHeaderComponent={
              <div className="d-flex justify-content-between">
                <div className="position-relative">
                  <Button onClick={toggleSidebar} color="primary">
                    {t("teams.table.createTeamButton")}
                  </Button>
                </div>
              </div>
            }
          />
        </div>
      </Card>
      <AddUserModal
        data={data}
        isOpen={showAddUserModal}
        teamSelected={teamSelected}
        toggle={() => setShowAddUserModal(!showAddUserModal)}
        onUserAdd={() => dispatchUpdate()}
      />
      <Sidebar
        open={sidebarOpen}
        toggleSidebar={toggleSidebar}
        onCreate={handleCreateEmployee}
        defaultValue={teamSelected}
        onEdit={handleOnEdit}
      />
    </Fragment>
  );
};

export default TeamList;
