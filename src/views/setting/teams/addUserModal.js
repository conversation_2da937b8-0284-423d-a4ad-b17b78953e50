import { useState, useEffect } from "react";
import DataTable from "react-data-table-component";
import { Input, Modal, ModalBody, ModalHeader } from "reactstrap";
import { useTranslation } from "react-i18next"; // Importamos useTranslation
import { columnsEmployees } from "./columnsEmployees";
import { getAllEmployees } from "../../../services/company";
import {
  addEmployeeToTeam,
  removeEmployeeFromTeam,
} from "../../../services/teams";
import { toast } from "react-hot-toast";

function AddUserModal({
  isOpen,
  toggle,
  onClose = () => {},
  onUserAdd = () => {},
  teamSelected,
  data = [],
}) {
  const { t } = useTranslation(); // Usamos el hook useTranslation
  const [name, setName] = useState("");
  const [users, setUsers] = useState([]);
  const [filteredUsers, setFilteredUsers] = useState([]);

  const getUsers = async () => {
    const response = await getAllEmployees();
    setUsers(response);
    setFilteredUsers(response);
  };

  const handleOnFilter = (name) => {
    if (name === "" || !name) {
      setFilteredUsers(users);
      return;
    }

    const filtered = users.filter((user) => {
      return user.name.toLowerCase().includes(name.toLowerCase());
    });

    setFilteredUsers(filtered);
  };

  const handleOnEmployeeRemove = async (employeeId) => {
    removeEmployeeFromTeam({
      employeeId: [employeeId],
      teamId: teamSelected,
    })
      .then(() => {
        toast.success(t("teams.addUserModal.userRemovedSuccessfully"));
        onUserAdd();
      })
      .catch(() => {
        toast.error(t("teams.addUserModal.failedToRemoveUser"));
      });
  };

  const handleOnAdd = async (employeeId) => {
    addEmployeeToTeam({
      employeeId: [employeeId],
      teamId: teamSelected,
    })
      .then(() => {
        toast.success(t("teams.addUserModal.userAddedSuccessfully"));
        onUserAdd();
      })
      .catch(() => {
        toast.error(t("teams.addUserModal.failedToAddUser"));
      });
  };

  useEffect(() => {
    getUsers();

    return () => {
      setUsers([]);
      setFilteredUsers([]);
      setName("");
    };
  }, []);

  useEffect(() => {
    handleOnFilter(name);
  }, [name]);

  return (
    <Modal
      isOpen={isOpen}
      onClosed={onClose}
      toggle={toggle}
      className="modal-dialog-centered modal-lg"
    >
      <ModalHeader className="bg-transparent m-0" toggle={toggle}>
        <div className="w-100 d-flex align-items-start">
          <label className="mb-0" htmlFor="search-invoice">
            {t("teams.addUserModal.search")}:{" "}
            {/* Usamos las traducciones aquí */}
          </label>
          <Input
            type="text"
            id="search-invoice"
            className="w-100"
            value={name}
            name="searchName"
            placeholder={t("teams.addUserModal.searchPlaceholder")}
            onChange={(e) => setName(e.target.value)}
          />
        </div>
      </ModalHeader>
      <ModalBody>
        <DataTable
          pagination
          responsive
          columns={columnsEmployees(
            data.filter((team) => team.id === teamSelected)[0]?.employees,
            handleOnAdd,
            handleOnEmployeeRemove,
            t
          )}
          className="react-dataTable overflow-visible"
          data={filteredUsers}
        />
      </ModalBody>
    </Modal>
  );
}

export default AddUserModal;
