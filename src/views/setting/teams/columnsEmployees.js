import { Plus, Trash2 } from "react-feather";
import { But<PERSON> } from "reactstrap";

import Avatar from "@components/avatar";

const renderClient = (fullName) => {
  return (
    <Avatar
      initials
      className="me-1"
      color={"light-primary"}
      content={fullName || "None"}
    />
  );
};

const checkIfUserExists = (employees = [], userId) => {
  return employees.some((user) => {
    return user.id === userId;
  });
};

export const columnsEmployees = (employees, onAdd, onRemove, t) => [
  {
    name: t("teams.addUserModal.column.userName"),
    sortable: false,
    minWidth: "300px",
    selector: (row) => row.name,
    cell: (row) => (
      <div className="d-flex justify-content-left align-items-center">
        {renderClient(row.name)}
        <div className="d-flex flex-column">
          <span className="fw-bolder text-uppercase ">{row.name}</span>
          <small className="text-truncate text-muted mb-0 text-uppercase">
            {row.email}
          </small>
        </div>
      </div>
    ),
  },
  {
    name: "",
    minWidth: "100px",
    cell: (row) => (
      <div className="form-check">
        {checkIfUserExists(employees, row.id) ? (
          <Button color="primary" outline onClick={() => onRemove(row.id)}>
            <Trash2 size={14} />
            <span className="align-middle ms-25">{t("general.delete")}</span>
          </Button>
        ) : (
          <Button color="primary" outline onClick={() => onAdd(row.id)}>
            <Plus size={14} />
            <span className="align-middle ms-25">{t("general.add")}</span>
          </Button>
        )}
      </div>
    ),
  },
];
