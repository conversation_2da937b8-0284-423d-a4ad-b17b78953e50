import React, { useEffect, useState } from "react";
import { toast } from "react-hot-toast";
import { useTranslation } from "react-i18next";
import withReactContent from "sweetalert2-react-content";
import Swal from "sweetalert2";

import TeamList from "./TeamList";
import {
  createTeam,
  deleteTeam,
  getTeamsList,
  updateTeam,
} from "../../../services/teams";

import "@styles/react/apps/app-users.scss";

const Teams = () => {
  const { t } = useTranslation();
  const NotificationSwal = withReactContent(Swal);

  const [teams, setTeams] = useState([]);

  const getTeams = async () => {
    const response = await getTeamsList();
    setTeams(response);
  };

  const handleDelete = async (id) => {
    return NotificationSwal.fire({
      title: t("teams.deleteTeamConfirmation"),
      text: t("teams.cannotRevertAction"),
      icon: "warning",
      showCancelButton: true,
      confirmButtonText: t("teams.deleteTeamBtn"),
      customClass: {
        confirmButton: "btn btn-primary",
        cancelButton: "btn btn-outline-danger ms-1",
      },
      buttonsStyling: false,
    }).then(async (result) => {
      if (result.value) {
        await deleteTeam(id)
          .then(() => toast.success(t("teams.deleteTeamSuccess")))
          .catch(() => {
            toast.error(t("teams.deleteTeamError"));
          });

        getTeams();
      }
    });
  };

  const handleCreate = async (data, callback) => {
    createTeam(data)
      .then(() => {
        getTeams();
        toast.success(t("teams.teamCreatedSuccessfully"));
        callback();
      })
      .catch(() => {
        toast.error(t("teams.deleteTeamError"));
        return;
      });
  };

  const handleEdit = async (teamId, team) => {
    updateTeam(teamId, team)
      .then(() => {
        toast.success(t("teams.updateTeamSuccess"));
        getTeams();
      })
      .catch(() => {
        toast.error(t("teams.updateTeamError"));
      });
  };

  useEffect(() => {
    getTeams();
  }, []);

  return (
    <div className="app-user-list">
      <TeamList
        data={teams}
        onCreate={handleCreate}
        onDelete={handleDelete}
        onEdit={handleEdit}
        dispatchUpdate={getTeams}
      />
    </div>
  );
};

export default Teams;
