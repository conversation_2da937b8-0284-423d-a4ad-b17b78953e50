import { Fragment, useState } from "react";
import { <PERSON><PERSON>, <PERSON> } from "reactstrap";
import DataTable from "react-data-table-component";

import Sidebar from "./Sidebar";
import { columns } from "./columns";

import "@styles/react/libs/react-select/_react-select.scss";
import "@styles/react/libs/tables/react-dataTable-component.scss";
import EmptyState from "../../../@core/components/empty-state/empty-state-table";

const UsersList = ({ data, onCreate, onDelete }) => {
  const [sidebarOpen, setSidebarOpen] = useState(false);

  const toggleSidebar = () => {
    setSidebarOpen(!sidebarOpen);
  };

  const handleCreateEmployee = async (data) => {
    try {
      await onCreate(data, toggleSidebar);
    } catch (error) {
      return;
    }
  };

  const handleOnDelete = async (id) => {
    await onDelete(id);
  };

  return (
    <Fragment>
      <Card>
        <div className="react-dataTable">
          <DataTable
            subHeader
            pagination
            responsive
            noDataComponent={
              <EmptyState
                title="Datos no encontrados"
                message="Parece que aun no creas ningún equipo, procede a crear una en el botón superior."
              />
            }
            columns={columns(handleOnDelete)}
            className="react-dataTable overflow-visible"
            data={data}
            subHeaderComponent={
              <div className="d-flex justify-content-between">
                <div className="position-relative">
                  <Button onClick={toggleSidebar} color="primary">
                    Crear equipo
                  </Button>
                </div>
              </div>
            }
          />
        </div>
      </Card>

      <Sidebar
        open={sidebarOpen}
        toggleSidebar={toggleSidebar}
        onCreate={handleCreateEmployee}
      />
    </Fragment>
  );
};

export default UsersList;
