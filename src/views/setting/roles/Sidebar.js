import { useState } from "react";
import { Button, Label, Form, Input } from "reactstrap";

import Sidebar from "@components/sidebar";

const SidebarNewUsers = ({ open, toggleSidebar, onCreate }) => {
  const [name, setName] = useState("");

  const onSubmit = async (e) => {
    e.preventDefault();
    await onCreate({
      name: name,
    });
  };

  const handleSidebarClosed = () => {};

  const handleToggleSidebar = () => {
    toggleSidebar();
    setName("");
  };

  return (
    <Sidebar
      size="lg"
      open={open}
      title="Nuevo equipo"
      headerClassName="mb-1"
      contentClassName="pt-0"
      toggleSidebar={handleToggleSidebar}
      onClosed={handleSidebarClosed}
    >
      <Form onSubmit={onSubmit}>
        <div className="mb-1">
          <Label className="form-label" for="fullName">
            Nombre del equipo <span className="text-danger">*</span>
          </Label>
          <Input
            id="name"
            required
            placeholder="Nombre del equipo"
            value={name}
            name="name"
            onChange={(e) => setName(e.target.value)}
          />
        </div>
        <Button type="submit" className="me-1" color="primary">
          Crear
        </Button>
        <Button type="reset" color="secondary" outline onClick={toggleSidebar}>
          Cancelar
        </Button>
      </Form>
    </Sidebar>
  );
};

export default SidebarNewUsers;
