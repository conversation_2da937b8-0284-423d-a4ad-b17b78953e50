import React from "react";
import { Input } from "reactstrap";
import "./styles.scss";
import { useTranslation } from "react-i18next";

const PermissionList = ({
  data,
  permissionSelected,
  handlePermissionSelect,
}) => {
  const renderGroupedPermissions = () => {
    const { t } = useTranslation();

    const groupedPermissions = data?.permission?.reduce((acc, role) => {
      if (!acc[role.moduleName]) {
        acc[role.moduleName] = [];
      }
      acc[role.moduleName].push(role);
      return acc;
    }, {});

    const setNameModel = (moduleName) => {
      if (!moduleName) {
        return;
      }
      switch (moduleName) {
        case "chat_session":
          return "Chats";
        case "bot_company":
          return "Bots";
        case "companies":
          return "Empresa";
        case "employee":
          return "Usuarios";
        case "permission":
          return "Permisos";
        case "roles":
          return "Roles";
        case "statistic":
          return "Estadisticas";
      }
    };

    return (
      <>
        {Object.entries(groupedPermissions).map(
          ([moduleName, permissions], index) => (
            <div key={index}>
              <h2 className="module-name-container">
                {setNameModel(moduleName)}
              </h2>
              <div>
                {permissions.map((role, roleIndex) => (
                  <div key={roleIndex} className="list-permission-container">
                    <div className="item">
                      <h4 className="text-capitalize">
                        {t(role.displayName)} - {role.name}
                      </h4>
                    </div>
                    <div className="item">
                      <Input
                        type="checkbox"
                        checked={permissionSelected.includes(role.id)}
                        id={`read-${role.id}`}
                        onChange={() => {
                          handlePermissionSelect(role.id);
                        }}
                      />
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )
        )}
      </>
    );
  };

  return <div>{renderGroupedPermissions()}</div>;
};

export default PermissionList;
