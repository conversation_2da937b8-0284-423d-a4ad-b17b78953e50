import { Fragment, useState } from "react";
import { <PERSON> } from "react-router-dom";
import {
  Row,
  Col,
  Card,
  Label,
  Input,
  Modal,
  Button,
  CardBody,
  ModalBody,
  ModalHeader,
  FormFeedback,
  UncontrolledTooltip,
} from "reactstrap";
import { Info, Trash } from "react-feather";
import { useForm, Controller } from "react-hook-form";
import { toast } from "react-hot-toast";
import withReactContent from "sweetalert2-react-content";
import Swal from "sweetalert2";
import { useTranslation } from "react-i18next";

import illustration from "@src/assets/images/illustration/faq-illustrations.svg";

import {
  assignPermissionToRole,
  createRole,
  updateRole,
} from "../../../services/roles";
import PermissionList from "./PermissionList";
import "./styles.scss";

const RoleCards = ({ data, onCreate, onDelete }) => {
  const NotificationSwal = withReactContent(Swal);
  const { t } = useTranslation();

  const [show, setShow] = useState(false);
  const [currentId, setCurrentId] = useState({});
  const [currentName, setCurrentName] = useState("");
  const [modalStatusName, setModalStatusName] = useState(false);
  const [permissionSelected, setPermissionSelected] = useState([]);

  const {
    reset,
    control,
    setError,
    setValue,
    handleSubmit,
    formState: { errors },
  } = useForm({ defaultValues: { roleName: "" } });

  const onSubmit = async () => {
    manageRequest();
  };

  const handlePermissionSelect = (permission) => {
    if (permissionSelected.includes(permission)) {
      setPermissionSelected(
        permissionSelected.filter((item) => item !== permission)
      );
    } else {
      setPermissionSelected([...permissionSelected, permission]);
    }
  };

  const handleSelectAll = () => {
    const isSelected = permissionSelected?.length === data?.permission?.length;

    if (isSelected) {
      setPermissionSelected([]);
    } else {
      setPermissionSelected(data?.permission?.map((item) => item.id));
    }
  };

  const onReset = () => {
    setShow(false);
    reset({ roleName: "" });
  };

  const handleModalClosed = () => {
    setValue("roleName");
  };

  const handleOnDelete = async (id) => {
    return NotificationSwal.fire({
      title: t("role.roleCards.deleteRoleConfirmation"),
      text: t("role.roleCards.cannotRevertAction"),
      icon: "warning",
      showCancelButton: true,
      confirmButtonText: t("role.roleCards.yesDelete"),
      customClass: {
        confirmButton: "btn btn-primary",
        cancelButton: "btn btn-outline-danger ms-1",
      },
      buttonsStyling: false,
    }).then(async (result) => {
      if (result.value) {
        onDelete(id);
        toast.success(t("role.roleCards.roleDeletedSuccessfully"));
      }
    });
  };

  const manageModalAction = (type, e = {}, showVal = true, item = "") => {
    if (type === "edit") {
      e.preventDefault();
      setShow(showVal);
      setCurrentId(item.id);
      setCurrentName(item.name);
      updateEditedModeData(item);
      setModalStatusName(true);
    } else {
      setShow(showVal);
      setModalStatusName(false);
    }
  };

  const updateEditedModeData = (data) => {
    let newGroup = [];
    for (let index = 0; index < data.permissions.length; index++) {
      newGroup.push(data.permissions[index].id);
    }
    setPermissionSelected(newGroup);
  };

  const manageRequest = async () => {
    if (!modalStatusName) {
      create({
        name: currentName,
        permissions: permissionSelected,
      });
      return;
    }

    update({
      rolId: currentId,
      permissions: permissionSelected,
    });
  };

  const create = async (data) => {
    const response = await createRole(data);

    if (response.error) {
      setError("roleName", {
        type: "manual",
        message: response.error,
      });
      return toast.error(response.error);
    }

    toast.success(t("role.roleCards.roleCreatedSuccessfully"));
    onCreate();
    setShow(false);
  };

  const update = async (data) => {
    const response = await updateRole(currentId, {
      name: currentName,
      permissions: permissionSelected,
    });

    await assignPermissionToRole(data);

    if (response.error) {
      setError("roleName", {
        type: "manual",
        message: response.error,
      });
      return toast.error(response.error);
    }

    toast.success(t("role.roleCards.roleUpdatedSuccessfully"));
    onCreate();
    setShow(false);
  };

  return (
    <Fragment>
      <div className="d-flex gap-2 flex-wrap role-container">
        {data?.roles?.map((item, index) => {
          return (
            <Card key={`role-${index}-${item.name}`}>
              <CardBody>
                <div className="d-flex justify-content-between">
                  <span>
                    {t("role.roleCards.usersAssociated", {
                      count: item.employees.length,
                    })}
                  </span>
                  <Button
                    color="outline-danger"
                    size="sm"
                    disabled={data?.roles?.length === 1}
                    className="text-nowrap d-flex align-items-center gap-1"
                    onClick={() => {
                      handleOnDelete(item.id);
                    }}
                  >
                    <Trash size={14} />
                    {t("role.roleCards.deleteRoleBtn")}
                  </Button>
                </div>
                <div className="d-flex justify-content-between align-items-end mt-1 pt-25">
                  <div className="role-heading">
                    <h4 className="fw-bolder">{item.name}</h4>
                    <Link
                      to="/"
                      className="role-edit-modal"
                      onClick={(e) => {
                        manageModalAction("edit", e, true, item);
                      }}
                    >
                      <small className="fw-bolder">
                        {t("role.roleCards.editRoleLink")}
                      </small>
                    </Link>
                  </div>
                </div>
              </CardBody>
            </Card>
          );
        })}
        <Col xl={4} md={6}>
          <Card>
            <Row>
              <Col sm={5}>
                <div className="d-flex align-items-end justify-content-center h-100">
                  <img
                    className="img-fluid mt-2"
                    src={illustration}
                    alt="Image"
                    width={85}
                  />
                </div>
              </Col>
              <Col sm={7}>
                <CardBody className="text-sm-end text-center ps-sm-0">
                  <Button
                    color="primary"
                    className="text-nowrap mb-1"
                    onClick={() => {
                      manageModalAction("new");
                    }}
                  >
                    {t("role.roleCards.addNewRoleBtn")}
                  </Button>
                  <p className="mb-0">
                    {t("role.roleCards.addRoleDescription")}
                  </p>
                </CardBody>
              </Col>
            </Row>
          </Card>
        </Col>
      </div>
      <Modal
        isOpen={show}
        onClosed={handleModalClosed}
        toggle={() => setShow(!show)}
        className="modal-dialog-centered"
        size="lg"
      >
        <ModalHeader
          className="bg-transparent"
          toggle={() => setShow(!show)}
        ></ModalHeader>
        <ModalBody className="px-5 pb-5">
          <div className="text-center mb-4">
            <h1>
              {modalStatusName
                ? t(`role.roleCards.editRoleLink`)
                : t(`role.roleCards.addNewRoleBtn`)}{" "}
            </h1>
            <p>{t("role.roleCards.setRolePermissions")}</p>
          </div>
          <Row tag="form" onSubmit={handleSubmit(onSubmit)}>
            <Col xs={12}>
              <Label className="form-label" check for="roleName">
                {t("role.roleCards.roleNameLabel")}
              </Label>
              <Controller
                name="roleName"
                control={control}
                render={({ field }) => (
                  <Input
                    {...field}
                    id="roleName"
                    value={currentName}
                    placeholder={t("role.roleCards.enterRoleName")}
                    invalid={errors.roleName && true}
                    onChange={(e) => setCurrentName(e.target.value)}
                  />
                )}
              />
              {errors.roleName && (
                <FormFeedback>
                  {t("role.roleCards.pleaseEnterValidRoleName")}
                </FormFeedback>
              )}
            </Col>
            <Col xs={12}>
              <h4 className="mt-2 pt-50">
                {t("role.roleCards.rolePermissions")}
              </h4>
              <div className="list-permission-container">
                <div>
                  <span className="me-50">{t("role.roleCards.selectAll")}</span>
                  <Info size={14} id="info-tooltip" />
                  <UncontrolledTooltip placement="top" target="info-tooltip">
                    {t("role.roleCards.infoTooltip")}
                  </UncontrolledTooltip>
                </div>
                <div>
                  <div className="select-all-container">
                    <Input
                      type="checkbox"
                      id="select-all"
                      checked={
                        permissionSelected?.length === data?.permission?.length
                      }
                      onChange={handleSelectAll}
                    />
                  </div>
                </div>
              </div>

              <PermissionList
                data={data}
                permissionSelected={permissionSelected}
                handlePermissionSelect={handlePermissionSelect}
              />
            </Col>
            <Col className="text-center mt-2" xs={12}>
              <Button type="submit" color="primary" className="me-1">
                {t("general.submit")}
              </Button>
              <Button type="reset" outline onClick={onReset}>
                {t("general.discard")}
              </Button>
            </Col>
          </Row>
        </ModalBody>
      </Modal>
    </Fragment>
  );
};

export default RoleCards;
