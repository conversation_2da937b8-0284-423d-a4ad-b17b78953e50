import { MoreVertical, Trash2 } from "react-feather";
import {
  UncontrolledDropdown,
  DropdownToggle,
  DropdownMenu,
  DropdownItem,
} from "reactstrap";
import moment from "moment";

import Avatar from "@components/avatar";

const renderClient = (fullName) => {
  return (
    <Avatar
      initials
      className="me-1"
      color={"light-primary"}
      content={fullName || "None"}
    />
  );
};

export const columns = (onDelete) => [
  {
    name: "Nombre del equipo",
    sortable: false,
    minWidth: "300px",
    selector: (row) => row.name,
    cell: (row) => (
      <div className="d-flex justify-content-left align-items-center">
        {renderClient(row.name)}
        <div className="d-flex flex-column">
          <span className="fw-bolder text-uppercase ">{row.name}</span>
          <small className="text-truncate text-muted mb-0 text-uppercase">
            {row.email}
          </small>
        </div>
      </div>
    ),
  },
  {
    name: "<PERSON><PERSON> de creación",
    minWidth: "230px",
    sortable: true,
    sortField: "created_at",
    selector: (row) => row.created_at,
    cell: (row) => (
      <span className="text-capitalize">
        {moment(row.created_at).format("DD/MM/YYYY")}
      </span>
    ),
  },
  {
    name: "Acciones",
    minWidth: "100px",
    cell: (row) => (
      <div className="column-action">
        <UncontrolledDropdown>
          <DropdownToggle tag="div" className="btn btn-sm">
            <MoreVertical size={14} className="cursor-pointer" />
          </DropdownToggle>
          <DropdownMenu>
            <DropdownItem
              tag="a"
              href="/"
              className="w-100"
              onClick={(e) => {
                e.preventDefault();
                onDelete(row.id);
              }}
            >
              <Trash2 size={14} className="me-50" />
              <span className="align-middle">Eliminar</span>
            </DropdownItem>
          </DropdownMenu>
        </UncontrolledDropdown>
      </div>
    ),
  },
];
