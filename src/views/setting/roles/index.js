import { useEffect, useState } from "react";
import { toast } from "react-hot-toast";
import { useTranslation } from "react-i18next";

import "@styles/react/apps/app-users.scss";
import RoleCards from "./RoleCards";
import { deleteRole, getRoleList } from "../../../services/roles";

const Roles = () => {
  const [roles, setRoles] = useState([]);
  const { t } = useTranslation();

  const getRoles = async () => {
    const response = await getRoleList();
    setRoles(response);
  };

  const handleDelete = async (id) => {
    deleteRole(id)
      .then(() => toast.success(t("role.toastSuccess")))
      .catch(() => toast.error(t("role.toastError")));

    getRoles();
  };

  useEffect(() => {
    getRoles();
  }, []);

  return (
    <div className="app-user-list">
      <h3>{t("role.rolesList")}</h3>
      <p className="mb-2">{t("role.description")}</p>
      <RoleCards
        data={roles}
        onCreate={(id) => getRoles(id)}
        onDelete={handleDelete}
      />
    </div>
  );
};

export default Roles;
