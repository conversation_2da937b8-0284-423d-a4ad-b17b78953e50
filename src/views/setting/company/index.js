import { useEffect, useState } from "react";
import { Row, Col } from "reactstrap";
import { toast } from "react-hot-toast";

import "@styles/react/apps/app-users.scss";

import CompanyTab from "./CompanyTab";
import { getCompanyInfo, updateCompanyInfo } from "../../../services/company";
import { useTranslation } from "react-i18next";

const UserView = () => {
  const { t } = useTranslation();

  const [companyInfo, setCompanyInfo] = useState({});

  const getCompanyInformation = async () => {
    const response = await getCompanyInfo();
    setCompanyInfo(response);
  };

  const updateCompanyInformation = async (data) => {
    await updateCompanyInfo(data);
    toast.success(t("general.infoUpdated"));
  };

  useEffect(() => {
    getCompanyInformation();
  }, []);

  return (
    <div className="app-user-view">
      <Row>
        <Col xl="12" lg="12" xs={{ order: 0 }} md={{ order: 1, size: 7 }}>
          <CompanyTab
            handleOnSubmit={updateCompanyInformation}
            data={companyInfo}
          />
        </Col>
      </Row>
    </div>
  );
};
export default UserView;
