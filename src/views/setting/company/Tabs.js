import { Nav, NavItem, NavLink } from "reactstrap";
import { User } from "react-feather";
import { useTranslation } from "react-i18next";

const Tabs = ({ activeTab, toggleTab }) => {
  const { t } = useTranslation();

  return (
    <Nav pills className="mb-2">
      <NavItem>
        <NavLink active={activeTab === "1"} onClick={() => toggleTab("1")}>
          <User size={18} className="me-50" />
          <span className="fw-bold">{t("general.account")}</span>
        </NavLink>
      </NavItem>
    </Nav>
  );
};

export default Tabs;
