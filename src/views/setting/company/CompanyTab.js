import { Fragment, useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import {
  Row,
  Col,
  Card,
  Form,
  Input,
  Button,
  CardBody,
  CardTitle,
  CardHeader,
  Label,
} from "reactstrap";
import "cleave.js/dist/addons/cleave-phone.us";

const CompanyTab = ({ data, handleOnSubmit }) => {
  const { t } = useTranslation();
  const [companyInfo, setCompanyInfo] = useState();

  const onSubmit = (e) => {
    e.preventDefault();
    handleOnSubmit(companyInfo);
  };

  const handleInputChange = (e) => {
    const companyMutation = companyInfo;
    delete companyMutation.botCompany;

    setCompanyInfo({
      ...companyInfo,
      [e.target.name]: e.target.value,
    });
  };

  useEffect(() => {
    setCompanyInfo(data);
  }, [data]);

  return (
    <Fragment>
      <Card>
        <CardHeader>
          <CardTitle tag="h4">{t("company.companyTitle")}</CardTitle>
        </CardHeader>
        <CardBody>
          <Form onSubmit={onSubmit}>
            <Row>
              <Col className="mb-2" md={6}>
                <Label>{t("company.companyName")}</Label>
                <Input
                  name="name"
                  className="input-group-merge"
                  value={companyInfo?.name}
                  onChange={handleInputChange}
                />
              </Col>
              <Col className="mb-2" md={6}>
                <Label>{t("company.principalActivity")}</Label>
                <Input
                  className="input-group-merge"
                  name="companyPrincipalActivity"
                  value={companyInfo?.companyPrincipalActivity}
                  onChange={handleInputChange}
                />
              </Col>
              <Col className="mb-2" md={6}>
                <Label>{t("company.website")}</Label>
                <Input
                  className="input-group-merge"
                  type="url"
                  name="websiteUrl"
                  value={companyInfo?.websiteUrl}
                  onChange={handleInputChange}
                />
              </Col>
              <Col className="mb-2" md={6}>
                <Label>{t("company.physicalAddress")}</Label>
                <Input
                  name="address"
                  className="input-group-merge"
                  value={companyInfo?.address}
                  onChange={handleInputChange}
                />
              </Col>
              <Col className="mb-2" md={6}>
                <Label>{t("company.email")}</Label>
                <Input
                  className="input-group-merge"
                  name="email"
                  value={companyInfo?.email}
                  onChange={handleInputChange}
                />
              </Col>

              <Col className="mb-2" md={6}>
                <Label>{t("company.companyDescription")}</Label>
                <Input
                  type="textarea"
                  name="companyDescription"
                  value={companyInfo?.companyDescription}
                  onChange={handleInputChange}
                  rows="3"
                  placeholder="Textarea"
                />
              </Col>
              <Col xs={12}>
                <Button type="submit" color="primary">
                  {t("company.saveButton")}
                </Button>
              </Col>
            </Row>
          </Form>
        </CardBody>
      </Card>
    </Fragment>
  );
};

export default CompanyTab;
