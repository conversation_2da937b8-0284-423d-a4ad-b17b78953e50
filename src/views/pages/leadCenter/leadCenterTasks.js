import {
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Dropdown<PERSON><PERSON>,
  DropdownMenu,
  DropdownToggle,
  UncontrolledDropdown,
} from "reactstrap";
import { MoreVertical } from "react-feather";

import WhatsappLogo from "../../../assets/logo/whatsapp.png";
import webIcon from "../../../assets/images/icons/internet.png";

const KanbanTasks = (props) => {
  const { task, onDelete, onArchive, onClick } = props;

  const handleTaskClick = (e) => {
    if (
      e.target.tagName === "svg" ||
      e.target.tagName === "BUTTON" ||
      e.target.tagName === "img"
    ) {
      return;
    }

    onClick(task.chatSession);
  };

  const renderLabels = () => {
    if (task?.chatSession?.tags?.length > 0) {
      return (
        <div className="d-flex gap-1">
          {task?.chatSession?.tags.map((label, index) => {
            return (
              <Badge key={index} label={label} color="light-primary">
                {label}
              </Badge>
            );
          })}
        </div>
      );
    } else {
      return null;
    }
  };

  return (
    <Card onClick={handleTaskClick} className="task" data-task-id={task?.id}>
      <CardHeader>
        <div className="d-flex align-items-center gap-2">
          <div>
            <img
              src={
                task.chatSession.botCompany.chatProvider === "whatsapp"
                  ? WhatsappLogo
                  : webIcon
              }
              width="30px"
            />
          </div>
          <h5 className="task-title">{task?.title}</h5>
        </div>
        <UncontrolledDropdown className="more-options-dropdown">
          <DropdownToggle className="btn-icon" color="transparent" size="sm">
            <MoreVertical size={20} />
          </DropdownToggle>
          <DropdownMenu end>
            <DropdownItem
              onClick={(e) => {
                e.preventDefault();
                onArchive(task.id);
              }}
            >
              Cliente no potencial
            </DropdownItem>
            <DropdownItem
              onClick={(e) => {
                e.preventDefault();
                onDelete(task.id);
              }}
            >
              Eliminar chat
            </DropdownItem>
          </DropdownMenu>
        </UncontrolledDropdown>
      </CardHeader>
      <CardBody data-task-id={task.id}>
        <b>AI annotation</b>
        <p>{task.aiDescription}</p>
      </CardBody>
      <CardFooter>{renderLabels()}</CardFooter>
    </Card>
  );
};

export default KanbanTasks;
