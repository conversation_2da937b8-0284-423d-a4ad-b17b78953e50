@import "../../../@core/scss/base/bootstrap-extended/include";
@import "../../../@core/scss/base/components/include";

// Kanban App
// ========================================================================

.kanban-application {
  overflow: auto !important;
  .app-kanban-wrapper {
    width: 100%;
    min-height: 600px;
    padding-bottom: 20px;
    box-sizing: content-box;
    display: flex;
    overflow: auto !important;
    .board-wrapper {
      display: flex;
      flex-direction: column;
      padding: 20px;
      box-sizing: content-box;
      background-color: #fff;
      box-shadow: $card-box-shadow;
      min-width: 340px;

      .tasks-wrapper {
        min-height: 100%;
      }
      .more-options-dropdown {
        .btn-icon {
          padding-right: 0;
        }
      }
      &:not(:last-child) {
        margin-right: 2rem;
      }

      .board-container {
        flex: 1;
      }

      .board-header {
        padding: 10px;
        input.board-title {
          border: 0;
          font-weight: 500;
          font-size: 1.1rem;
          white-space: nowrap;
          background-color: transparent;

          &:hover,
          &:focus {
            background-color: $white;
          }
        }
      }

      .task {
        cursor: grab;
        margin-top: 1rem;
        margin-bottom: 1rem;
        max-width: 340px;
        width: 100%;

        .card-header {
          display: flex;
          align-items: center;
          justify-content: space-between;

          .task-title {
            margin: 0;
            max-width: 190px;
            text-overflow: ellipsis;
            overflow: hidden;
            white-space: nowrap;
          }

          .card-body {
            margin-top: 0;
            padding-top: 0 !important;
          }
        }

        .task-title {
          display: block;
          font-weight: 500;
        }

        .task-img {
          width: 100%;
          height: 100%;
          max-width: 260px;
          object-fit: cover;
        }

        .task-footer {
          .avatar img {
            width: 28px;
            height: 28px;
          }
        }
      }
    }
  }
}

.sidebar-kanban-modal {
  .modal-body {
    height: calc(100vh - 9rem);
  }
  .dropzone {
    position: relative;
    .single-file-image {
      height: 100%;
      width: 100%;
      object-fit: cover;
      position: absolute;
      border-radius: $border-radius;
    }
  }
}

.dark-layout {
  .kanban-application {
    .app-kanban-wrapper {
      .board-header {
        input.board-title {
          color: $white;
          &:hover {
            background-color: $theme-dark-card-bg;
          }
        }
      }
    }
  }
}
