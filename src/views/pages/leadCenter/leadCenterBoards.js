import { useState, Fragment, useEffect } from "react";
import { ReactSortable } from "react-sortablejs";

import LeadCenterTasks from "./leadCenterTasks";
import {
  deleteTask,
  markChatAsNoPotentialClient,
  sortTask,
} from "../../../services/leadCenter";

const KanbanBoard = (props) => {
  const { board, index, onSort, onTaskSelect } = props;

  const [boardData, setBoardData] = useState({});

  const handleSort = async (taskId, targetBoardId) => {
    await sortTask(taskId, targetBoardId);
    onSort();
  };

  const handleOnDelete = async (id) => {
    await deleteTask(id);
    onSort();
  };

  const handleNoPotentialClient = async (id) => {
    await markChatAsNoPotentialClient(id);
    onSort();
  };

  useEffect(() => {
    setBoardData(board);
  }, [board]);

  return (
    <Fragment key={index}>
      <div className="board-wrapper" data-board-id={boardData.id}>
        <div className="d-flex align-items-center justify-content-between">
          <div className="d-flex align-items-center board-header">
            <h3 className="text-bold">{boardData.title}</h3>
          </div>
        </div>
        <div data-board-id={boardData.id} className="board-container">
          <ReactSortable
            list={boardData?.tasks || []}
            group="shared-group"
            setList={(changes) =>
              setBoardData({ ...boardData, tasks: changes })
            }
            onEnd={(event) =>
              handleSort(
                event.item.dataset.taskId,
                event.to.parentElement.dataset.boardId
              )
            }
            className={`tasks-wrapper board-${boardData.id}`}
          >
            {boardData?.tasks?.length > 0 &&
              boardData?.tasks?.map((task, index) => {
                return (
                  <LeadCenterTasks
                    task={task}
                    key={`${task.boardId}-${index}`}
                    onDelete={(id) => handleOnDelete(id)}
                    onArchive={(id) => handleNoPotentialClient(id)}
                    onClick={onTaskSelect}
                  />
                );
              })}
          </ReactSortable>
        </div>
      </div>
    </Fragment>
  );
};

export default KanbanBoard;
