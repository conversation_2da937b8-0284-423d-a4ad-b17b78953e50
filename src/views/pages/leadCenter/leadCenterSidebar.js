import { useEffect, useState } from "react";
import { Modal } from "reactstrap";

import Chat from "../../../@core/components/chatView/Chat";
import { getTeamsList } from "../../../services/teams";

import "@styles/react/libs/flatpickr/flatpickr.scss";
import "@styles/react/libs/react-select/_react-select.scss";
import "@styles/react/libs/file-uploader/file-uploader.scss";
import "@styles/base/pages/app-chat-list.scss";
import { toggleAi } from "../../../services/chat";

const LeadCenterSidebar = (props) => {
  const { sidebarOpen, handleTaskSidebarToggle, chat } = props;

  const [teams, setTeams] = useState([]);

  const handleSidebarOpened = () => {};

  const handleSidebarClosed = () => {};

  const getAllTeams = async () => {
    const response = await getTeamsList();

    setTeams(response);
  };

  const toggleAiAssignment = async (sessionId) => {
    await toggleAi(sessionId);
  };

  useEffect(() => {
    getAllTeams();
  }, [chat]);

  return (
    <Modal
      isOpen={sidebarOpen}
      className="sidebar-50"
      size="50"
      contentClassName="p-0"
      onOpened={handleSidebarOpened}
      onClosed={handleSidebarClosed}
      toggle={handleTaskSidebarToggle}
      modalClassName="modal-slide-in sidebar-kanban-modal"
    >
      {sidebarOpen && (
        <Chat
          chatSelected={chat}
          handleOpenSideForMobile={() => {}}
          toggleAiAssignment={toggleAiAssignment}
          onChatAssignment={() => {}}
          teams={teams}
        />
      )}
    </Modal>
  );
};

export default LeadCenterSidebar;
