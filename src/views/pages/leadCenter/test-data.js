export const boardData = {
  boards: [
    {
      id: "todo",
      title: "TODO",
    },
    {
      id: "in-progress",
      title: "In Progress",
    },
    {
      id: "done",
      title: "Done",
    },
  ],
  tasks: [
    {
      id: 1,
      labels: ["UX"],
      boardId: "todo",
      description: "lorem",
      dueDate: new Date(),
      title: "Research FAQ page UX",
      attachments: [
        {
          name: "documentation.doc",
          img: <></>,
        },
        {
          name: "app.js",
          img: <></>,
        },
      ],
      comments: [
        {
          name: "<PERSON>",
          img: <></>,
          comment: "Complete this on priority",
        },
        {
          name: "<PERSON>",
          img: <></>,
          comment: "Complete this on priority",
        },
        {
          name: "<PERSON>",
          img: <></>,
          comment: "Complete this on priority",
        },
      ],
      assignedTo: [
        {
          title: "<PERSON> Geller",
          img: <></>,
        },
        {
          title: "<PERSON><PERSON>be Buffay",
          img: <></>,
        },
      ],
    },
    {
      id: 2,
      labels: ["Images"],
      boardId: "todo",
      coverImage: "https://via.placeholder.com/300x300",
      description: "lorem",
      dueDate: new Date(),
      title: "Find new images for the apps",
      comments: [],
      attachments: [
        {
          name: "book.pdf",
          img: <></>,
        },
        {
          name: "app.js",
          img: <></>,
        },
      ],
      assignedTo: [
        {
          title: "Rachel Green",
          img: <></>,
        },
      ],
    },
    {
      id: 3,
      labels: ["App"],
      attachments: [
        {
          name: "list.txt",
          img: <></>,
        },
        {
          name: "pdf.png",
          img: <></>,
        },
      ],
      boardId: "in-progress",
      description: "",
      dueDate: new Date(),
      title: "Review completed Apps",
      comments: [
        {
          name: "Chandler Bing",
          img: <></>,
          comment: "Complete this on priority",
        },
        {
          name: "Monica Geller",
          img: <></>,
          comment: "Complete this on priority",
        },
        {
          name: "Joey Tribbiani",
          img: <></>,
          comment: "Complete this on priority",
        },
        {
          name: "Rachel Green",
          img: <></>,
          comment: "Complete this on priority",
        },
        {
          name: "Ross Geller",
          img: <></>,
          comment: "Complete this on priority",
        },
        {
          name: "Pheobe Buffay",
          img: <></>,
          comment: "Complete this on priority",
        },
      ],
      assignedTo: [
        {
          title: "Monica Geller",
          img: <></>,
        },
        {
          title: "Chandler Bing",
          img: <></>,
        },
      ],
    },
    {
      id: 4,
      labels: ["Code Review"],
      attachments: [
        {
          name: "list.txt",
          img: <></>,
        },
        {
          name: "pdf.png",
          img: <></>,
        },
        {
          name: "documentation.doc",
          img: <></>,
        },
        {
          name: "app.js",
          img: <></>,
        },
      ],
      boardId: "in-progress",
      description: "",
      dueDate: new Date(),
      title: "Review Javascript Code",
      comments: [
        {
          name: "Chandler Bing",
          img: <></>,
          comment: "Complete this on priority",
        },
        {
          name: "Monica Geller",
          img: <></>,
          comment: "Complete this on priority",
        },
      ],
      assignedTo: [
        {
          title: "Joey Tribbiani",
          img: <></>,
        },
        {
          title: "Jerry Seinfeld",
          img: <></>,
        },
      ],
    },
    {
      id: 5,
      labels: ["Forms"],
      attachments: [
        {
          name: "list.txt",
          img: <></>,
        },
      ],
      boardId: "done",
      description: "",
      dueDate: new Date(),
      title: "Forms & Tables Section",
      comments: [
        {
          name: "Chandler Bing",
          img: <></>,
          comment: "Complete this on priority",
        },
        {
          name: "Monica Geller",
          img: <></>,
          comment: "Complete this on priority",
        },
      ],
      assignedTo: [
        {
          title: "Astro Kramer",
          img: <></>,
        },
        {
          title: "George Costanza",
          img: <></>,
        },
      ],
    },
    {
      id: 6,
      labels: ["Charts & Maps"],
      attachments: [
        {
          name: "documentation.doc",
          img: <></>,
        },
        {
          name: "app.js",
          img: <></>,
        },
        {
          name: "book.pdf",
          img: <></>,
        },
      ],
      boardId: "done",
      description: "",
      dueDate: new Date(),
      title: "Completed Charts & Maps",
      comments: [
        {
          name: "Elaine Benes",
          img: <></>,
          comment: "Complete this on priority",
        },
        {
          name: "Newman Knight",
          img: <></>,
          comment: "Complete this on priority",
        },
      ],
      assignedTo: [
        {
          title: "Charlie Kelly",
          img: <></>,
        },
        {
          title: "Dennis Reynolds",
          img: <></>,
        },
      ],
    },
  ],
};
