import { useEffect, useState } from "react";

import {
  <PERSON><PERSON>,
  But<PERSON>,
  Input,
  Label,
  Modal,
  <PERSON>dal<PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON>Header,
} from "reactstrap";
import { Settings } from "react-feather";
import withReactContent from "sweetalert2-react-content";
import Swal from "sweetalert2";
import { useTranslation } from "react-i18next";

import LeadCenterBoards from "./leadCenterBoards";
import { getLeadCenter, updateCriteria } from "../../../services/leadCenter";
import "./lead-center.scss";
import LeadCenterSidebar from "./leadCenterSidebar";

const KanbanBoard = () => {
  const { t } = useTranslation();
  const NotificationSwal = withReactContent(Swal);
  const [boardData, setBoardData] = useState();
  const [showCriteriaModal, setShowCriteriaModal] = useState(false);
  const [criteriaInputs, setCriteriaInputs] = useState([]);
  const [criteriaPotentialClient, setCriteriaPotentialClient] = useState("");
  const [chatSelected, setChatSelected] = useState();
  const [showChatModal, setShowChatModal] = useState(false);

  const getLeadCenterData = async () => {
    const leadCenter = await getLeadCenter();
    setBoardData(leadCenter);
    setCriteriaInputs(leadCenter?.criteriaInputs);
    setCriteriaPotentialClient(leadCenter?.criteriaPotentialClient);
  };

  const handleUpdateCriteria = async () => {
    try {
      await updateCriteria({
        criteriaInputs,
        criteriaPotentialClient,
      });
      getLeadCenterData();

      NotificationSwal.fire({
        icon: "success",
        title: "Critérios atualizados",
        text: "Los criterios han sido actualizados correctamente",
        customClass: {
          confirmButton: "btn btn-success",
        },
      });
    } catch (error) {
      NotificationSwal.fire({
        icon: "error",
        title: "Error!",
        text: "Error al actualizar los criterios, inténtalo de nuevo",
        customClass: {
          confirmButton: "btn btn-success",
        },
      });
    }
  };

  const openSidebar = (chat) => {
    setChatSelected(chat);
    setShowChatModal(true);
  };

  const closeSidebar = () => {
    setChatSelected(null);
    setShowChatModal(false);
  };

  useEffect(() => {
    getLeadCenterData();
  }, []);

  const renderBoards = () => {
    return boardData.boards.map((board, index) => (
      <LeadCenterBoards
        board={board}
        onSort={getLeadCenterData}
        key={`${board.id}-${index}`}
        index={`${board.id}-${index}`}
        onTaskSelect={openSidebar}
      />
    ));
  };

  return boardData?.boards ? (
    <div>
      <Button
        color="primary mb-1 d-flex align-items-center gap-1"
        onClick={() => setShowCriteriaModal(true)}
      >
        {t("leadCenter.editLeadTitle")} <Settings size={18} />
      </Button>
      <div className="app-kanban-wrapper">{renderBoards()}</div>
      <Modal
        isOpen={showCriteriaModal}
        size="lg"
        toggle={() => setShowCriteriaModal(false)}
        onClosed={() => setShowCriteriaModal(false)}
      >
        <ModalHeader>
          <h3>{t("leadCenter.editModal.title")}</h3>
        </ModalHeader>
        <ModalBody>
          <Alert color="primary" className="p-1">
            {t("leadCenter.editModal.subtitle")}
          </Alert>
          <Label>{t("leadCenter.editModal.step1")}</Label>
          <Input
            type="textarea"
            placeholder={t("leadCenter.editModal.step1InputPlaceholder")}
            value={criteriaPotentialClient}
            onChange={(e) => setCriteriaPotentialClient(e.target.value)}
          />
          <div className="divider"></div>
          <Alert color="primary" className="p-1">
            {t("leadCenter.editModal.step2")}
          </Alert>
          <div className="d-flex flex-column">
            <div className="form-check">
              <Label>{t("general.name")}</Label>
              <Input
                type="checkbox"
                name="name"
                value={criteriaInputs?.name}
                checked={criteriaInputs?.name}
                onChange={(e) =>
                  setCriteriaInputs({
                    ...criteriaInputs,
                    [e.target.name]: e.target.checked,
                  })
                }
              />
            </div>
            <div className="form-check">
              <Label>{t("general.email")}</Label>
              <Input
                type="checkbox"
                name="email"
                value={criteriaInputs.email}
                checked={criteriaInputs?.email}
                onChange={(e) =>
                  setCriteriaInputs({
                    ...criteriaInputs,
                    [e.target.name]: e.target.checked,
                  })
                }
              />
            </div>
            <div className="form-check">
              <Label>{t("general.contactNumber")}</Label>
              <Input
                type="checkbox"
                name="phone"
                value={criteriaInputs.phone}
                checked={criteriaInputs.phone}
                onChange={(e) =>
                  setCriteriaInputs({
                    ...criteriaInputs,
                    [e.target.name]: e.target.checked,
                  })
                }
              />
            </div>
          </div>
        </ModalBody>
        <ModalFooter>
          <Button color="primary" onClick={handleUpdateCriteria}>
            {t("general.update")}
          </Button>
        </ModalFooter>
      </Modal>
      <LeadCenterSidebar
        chat={chatSelected}
        sidebarOpen={showChatModal}
        handleTaskSidebarToggle={closeSidebar}
      />
    </div>
  ) : null;
};

export default KanbanBoard;
