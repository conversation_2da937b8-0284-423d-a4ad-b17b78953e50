// ** React Imports
import { Link } from 'react-router-dom'
import Logo from '../../../assets/logo/LOGO.png'
import './styles/index.scss'
// ** Reactstrap Imports
import { Button } from 'reactstrap'

// ** Custom Hooks
import { useSkin } from '@hooks/useSkin'

// ** Illustrations Imports
import illustrationsLight from '@src/assets/images/pages/error.svg'
import illustrationsDark from '@src/assets/images/pages/error-dark.svg'

// ** Styles
import '@styles/base/pages/page-misc.scss'

const Error = () => {
  // ** Hooks
  const { skin } = useSkin()

  const source = skin === 'dark' ? illustrationsDark : illustrationsLight

  return (
    <div className='misc-wrapper'>
      <a className='brand-logo' href='/'>
        <div className='logoContainer'>
          <img src={Logo} alt="logo" />
        </div>
      </a>
      <div className='misc-inner p-2 p-sm-3'>
        <div className='w-100 text-center'>
          <h2 className='mb-1'>¡Ups! 🕵🏻‍♀️ <br />¡Parece que te has perdido en el ciberespacio!</h2>
          <p className='mb-2'>No te preocupes, nuestros expertos están en ello. Mientras tanto, relájate y disfruta de un breve descanso. Pronto estaremos de vuelta para guiarte en la dirección correcta.
          
          <br/>
           ¡Gracias por tu paciencia!</p>
          <Button tag={Link} to='/' color='primary' className='btn-sm-block mb-2'>
            Vuelve al inicio
          </Button>
          <img className='img-fluid' src={source} alt='Not authorized page' />
        </div>
      </div>
    </div>
  )
}
export default Error
