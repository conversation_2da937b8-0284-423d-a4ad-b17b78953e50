import Sidebar from "@components/sidebar";
import { useEffect, useState } from "react";
import { Button, Col, Form, FormGroup, Input, Label, Row } from "reactstrap";

import { getCalendarList } from "../../../services/calendar";

const SidebarCompleteCalendarIntegration = ({
  open,
  toggleSidebar,
  onSubmit,
  botRequestId,
  isLoading,
  initialData,
}) => {
  const [formData, setFormData] = useState({
    calendarType: "new",
    calendarName: "",
    existingCalendar: "",
    availableDays: [],
    availableHoursStart: "09:00",
    availableHoursEnd: "18:00",
    sendNotifications: false,
    reminderTime: "30",
    appointmentIntervalValue: 30,
    appointmentIntervalUnit: "minutes",
  });
  const [availableCalendars, setAvailableCalendars] = useState(null);

  // Effect to update formData if initialData prop changes
  useEffect(() => {
    setFormData({
      calendarType: initialData?.calendarId ? "existing" : "new",
      calendarName: initialData?.calendarName || "",
      existingCalendar: initialData?.calendarId || "",
      availableDays: initialData?.availableDays.map((day) => Number(day)) || [],
      availableHoursStart: initialData?.availableHoursStart || "09:00",
      availableHoursEnd: initialData?.availableHoursEnd || "18:00",
      sendNotifications: typeof initialData?.reminderTime === 'number',
      reminderTime: initialData?.reminderTime ? String(initialData.reminderTime) : "30",
      appointmentIntervalValue: initialData?.intervalValue || 30,
      appointmentIntervalUnit: initialData?.intervalUnit || "minutes",
    });
  }, [initialData]);

  const handleToggleSidebar = () => {
    toggleSidebar();
  };

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;

    setFormData(prevFormData => {
      if (type === "checkbox") {
        if (name === "sendNotifications") {
          return {
            ...prevFormData,
            [name]: checked,
          };
        } else if (name === "availableDays") {
          const currentDays = prevFormData.availableDays;
          if (checked) {
            // Add the day if it's not already there
            return {
              ...prevFormData,
              availableDays: currentDays.includes(Number(value)) ? currentDays : [...currentDays, Number(value)],
            };
          } else {
            // Remove the day
            return {
              ...prevFormData,
              availableDays: currentDays.filter((day) => day !== Number(value)),
            };
          }
        } else {
          // For other potential single checkboxes
          return {
            ...prevFormData,
            [name]: checked,
          };
        }
      } else {
        // For non-checkbox inputs (text, select, etc.)
        return {
          ...prevFormData,
          [name]: value,
        };
      }
    });
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    const data = {
      calendarId:
        formData.calendarType === "existing" ? formData.existingCalendar : null,
      botRequestId,
      isExistingCalendar: formData.calendarType === "new" ? false : true,
      calendarName:
        formData.calendarType === "new"
          ? formData.calendarName
          : formData.existingCalendar,
      meetingInterval: {
        value: formData.appointmentIntervalValue,
        unit: formData.appointmentIntervalUnit,
      },
      availableDays: formData.availableDays,
      availableHoursStart: formData.availableHoursStart,
      availableHoursEnd: formData.availableHoursEnd,
      reminderTime: {
        enable: formData.sendNotifications,
        value: Number(formData?.reminderTime),
      },
    };

    onSubmit(data);
  };

  const handleCalendarList = async () => {
    const calendars = await getCalendarList(botRequestId)
      .then((res) => res)
      .catch((err) => err);

    setAvailableCalendars(calendars?.items || []);
  };

  const returnDayNames = (dayInNumber) => {
    const days = {
      1: "Lunes",
      2: "Martes",
      3: "Miércoles",
      4: "Jueves",
      5: "Viernes",
      6: "Sábado",
      7: "Domingo",
    };

    return days[dayInNumber];
  };

  useEffect(() => {
    if (botRequestId && formData.calendarType === "existing") {
      handleCalendarList();
    }
  }, [botRequestId, formData.calendarType]);

  return (
    <Sidebar
      size="25"
      open={open}
      title="Completar integracion"
      headerClassName="mb-1"
      contentClassName="pt-0"
      toggleSidebar={handleToggleSidebar}
    >
      <Form onSubmit={handleSubmit}>
        <h4 className="mb-4">Configuración de Google Calendar</h4>

        <FormGroup>
          <Label>Selecciona el calendario</Label>
          <Input
            type="select"
            name="calendarType"
            value={formData.calendarType}
            onChange={handleChange}
          >
            <option value="new">Crear un nuevo calendario</option>
            <option value="existing">Usar un calendario existente</option>
          </Input>

          {formData.calendarType === "new" && (
            <FormGroup className="mt-1">
              <Label>Nombre del Calendario</Label>
              <Input
                type="text"
                required
                name="calendarName"
                value={formData.calendarName}
                onChange={handleChange}
                placeholder="Ej: Citas de Clínica XYZ"
              />
            </FormGroup>
          )}

          {formData.calendarType === "existing" && (
            <FormGroup className="mt-1">
              <Label>Selecciona un calendario</Label>
              <Input
                type="select"
                required
                name="existingCalendar"
                value={formData.existingCalendar}
                onChange={handleChange}
              >
                {console.log(availableCalendars)}
                {availableCalendars?.map((calendar) => (
                  <option value={calendar?.id} key={calendar.id}>
                    {calendar.summary}
                  </option>
                ))}
              </Input>
            </FormGroup>
          )}
        </FormGroup>

        <FormGroup>
          <Label>Intervalo entre citas</Label>
          <Row>
            <Col xs="6">
              <Input
                required
                type="number"
                name="appointmentIntervalValue"
                value={formData.appointmentIntervalValue}
                onChange={handleChange}
                min="1"
                placeholder="Ej: 30"
              />
            </Col>
            <Col xs="6">
              <Input
                type="select"
                required
                name="appointmentIntervalUnit"
                value={formData.appointmentIntervalUnit}
                onChange={handleChange}
              >
                <option value="minutes">Minutos</option>
                <option value="hours">Horas</option>
              </Input>
            </Col>
          </Row>
        </FormGroup>

        <FormGroup>
          <Label>Días disponibles</Label>
          <Row>
            {console.log(formData.availableDays)}
            {[1, 2, 3, 4, 5, 6, 7].map((day) => (
              <Col key={day} xs="6" sm="4">
                <FormGroup check>
                  <Input
                    type="checkbox"
                    id={day}
                    required={
                      formData.availableDays.length === 0 ? true : false
                    }
                    name="availableDays"
                    value={day}
                    checked={formData.availableDays.includes(day)}
                    onChange={handleChange}
                  />
                  <Label check htmlFor={day}>
                    {returnDayNames(day)}
                  </Label>
                </FormGroup>
              </Col>
            ))}
          </Row>
        </FormGroup>

        <FormGroup>
          <Label>Horario disponible</Label>
          <Row>
            <Col xs="6">
              <Input
                type="time"
                required
                name="availableHoursStart"
                value={formData.availableHoursStart}
                onChange={handleChange}
              />
            </Col>
            <Col xs="6">
              <Input
                type="time"
                required
                name="availableHoursEnd"
                value={formData.availableHoursEnd}
                onChange={handleChange}
              />
            </Col>
          </Row>
        </FormGroup>

        <FormGroup switch>
          <Input
            type="switch"
            id="sendNotifications"
            name="sendNotifications"
            checked={formData.sendNotifications}
            onChange={handleChange}
          />
          <Label htmlFor="sendNotifications">
            Enviar recordatorios de citas
          </Label>
        </FormGroup>

        {formData.sendNotifications && (
          <FormGroup className="mt-1">
            <Label>Tiempo antes del recordatorio</Label>
            <Input
              type="select"
              required
              name="reminderTime"
              value={formData.reminderTime}
              onChange={handleChange}
            >
              <option value="10">10 minutos antes</option>
              <option value="30">30 minutos antes</option>
              <option value="60">1 hora antes</option>
            </Input>
          </FormGroup>
        )}

        <Button
          disabled={isLoading !== undefined && isLoading}
          color="primary"
          type="submit"
          block
          className="mt-3"
        >
          Guardar Configuración
        </Button>
      </Form>
    </Sidebar>
  );
};

export default SidebarCompleteCalendarIntegration;
