import { useEffect, useState } from "react";
import { Spinner } from "reactstrap";

import BotList from "./BotList";
import { getAllBots } from "../../../services/bot";

import "@styles/react/apps/app-users.scss";

const Bots = () => {
  const [bots, setBots] = useState([]);
  const [loading, setLoading] = useState(false);

  const getBotList = async () => {
    setLoading(true);
    const response = await getAllBots();
    setBots(response.botCompany);
    setLoading(false);
  };

  useEffect(() => {
    getBotList();
  }, []);

  return (
    <div className="app-user-list">
      {loading ? (
        <div className="d-flex justify-content-center align-items-center">
          <Spinner color="primary" />
        </div>
      ) : (
        <BotList data={bots} updateBotList={getBotList} />
      )}
    </div>
  );
};

export default Bots;
