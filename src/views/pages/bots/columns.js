import { Edit2, <PERSON>, MoreVertical, Trash2 } from "react-feather";
import {
  UncontrolledDropdown,
  DropdownToggle,
  DropdownMenu,
  DropdownItem,
  But<PERSON>,
} from "reactstrap";
import moment from "moment";

import whatsappIcon from "../../../assets/images/icons/whatsapp.png";
import webIcon from "../../../assets/images/icons/internet.png";
import instagram from "../../../assets/images/icons/social/instagram.png";

const RenderBotIcon = ({ provider }) => {
  if (provider === "whatsapp") {
    return <img width="20px" src={whatsappIcon} />;
  } else if (provider === "web") {
    return <img width="20px" src={webIcon} />;
  } else if (provider === "instagram") {
    return <img width="20px" src={instagram} />;
  }

  return null;
};

export const columns = (onDelete, onEdit, showIntegration, t) => [
  {
    name: t("bot.listOfBots.botName"),
    sortable: false,
    selector: (row) => console.log(row),
    cell: (row) => (
      <div className="d-flex justify-content-left align-items-center">
        <div className="d-flex align-items-center gap-1">
          <RenderBotIcon provider={row?.chatProvider} />
          <span className="fw-bolder">{row?.botName}</span>
        </div>
      </div>
    ),
  },
  {
    name: t("bot.listOfBots.publicKey"),
    sortable: false,
    selector: (row) => row?.botRequestId,
    cell: (row) => <span>{row?.botRequestId}</span>,
  },
  {
    name: t("bot.listOfBots.botType"),
    sortable: false,
    selector: (row) => row?.chatProvider,
    cell: (row) => <span className="text-uppercase">{row?.chatProvider}</span>,
  },
  {
    name: t("bot.listOfBots.integration"),
    sortable: false,
    minWidth: "auto",
    selector: (row) => row.id,
    cell: (row) => (
      <Button.Ripple
        outline
        color="primary"
        className="d-flex w-auto"
        onClick={() => showIntegration(row)}
      >
        <span className="align-middle text-nowrap d-flex gap-1">
          <Eye size={14} />
          {t("bot.listOfBots.seeIntegration")}
        </span>
      </Button.Ripple>
    ),
  },
  {
    name: t("general.active"),
    sortable: false,
    selector: (row) => row?.isBotEnabled,
    cell: (row) => (
      <span className="text-uppercase">
        {row?.isBotEnabled ? t("general.yes") : t("general.no")}
      </span>
    ),
  },
  {
    name: t("general.createdAt"),
    sortable: false,
    sortField: "created_at",
    selector: (row) => row?.createdAt,
    cell: (row) => (
      <span className="text-capitalize">
        {moment(row?.createdAt).format("DD/MM/YYYY")}
      </span>
    ),
  },
  {
    name: t("general.actions"),
    minWidth: "100px",
    cell: (row) => (
      <div className="column-action">
        <UncontrolledDropdown>
          <DropdownToggle tag="div" className="btn btn-sm">
            <MoreVertical size={14} className="cursor-pointer" />
          </DropdownToggle>
          <DropdownMenu>
            <DropdownItem
              tag="a"
              href="/"
              className="w-100"
              onClick={(e) => {
                e.preventDefault();
                onEdit(row.id);
              }}
            >
              <Edit2 size={14} className="me-50" />
              <span className="align-middle">
                {t("bot.listOfBots.editBot")}
              </span>
            </DropdownItem>
            <DropdownItem
              tag="a"
              href="/"
              className="w-100"
              onClick={(e) => {
                e.preventDefault();
                onDelete(row.id);
              }}
            >
              <Trash2 size={14} className="me-50" />
              <span className="align-middle">
                {t("bot.listOfBots.deleteBot")}
              </span>
            </DropdownItem>
          </DropdownMenu>
        </UncontrolledDropdown>
      </div>
    ),
  },
];
