import { useEffect, useState } from "react";

import { getDataSources } from "../../../services/dataSources";

import DataSource from "@components/datasource";

function DataSourcePage() {
  const [dataSource, setDataSource] = useState([]);

  const getDataSourcesList = async () => {
    const response = await getDataSources();
    setDataSource(response);
  };

  useEffect(() => {
    getDataSourcesList();
  }, []);

  return (
    <DataSource
      onContinue={getDataSourcesList}
      data={dataSource}
      showSelect={false}
      showCreateButton
    />
  );
}

export default DataSourcePage;
