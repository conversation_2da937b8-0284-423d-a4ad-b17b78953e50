import React from "react";
import "../styles/style.scss";

const SenderName = {
  SERVER: "SERVER",
  USER: "USER",
};

function MessageBubble(props) {
  const { message, primary, secondary } = props;
  console.log(primary);
  console.log(props);
  const senderClass = message.sender === SenderName.USER ? "sent" : "received";
  const setColor = message.sender === SenderName.SERVER ? primary : secondary;
  return (
    <div
      style={{ backgroundColor: `${setColor}` }}
      className={`message-bubble ${senderClass && senderClass}`}
    >
      <div className="message-text">{message.text && message.text}</div>
    </div>
  );
}

export default React.memo(MessageBubble);
