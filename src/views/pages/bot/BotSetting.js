import { Fragment, useEffect, useState } from "react";
import {
  Row,
  Col,
  Card,
  Form,
  Button,
  CardBody,
  CardTitle,
  CardHeader,
  Input,
} from "reactstrap";

import ChatVisualization from "../../../@core/components/chat-visualization";

const PaymentMethods = ({ botCompanyInfo, onUpdate }) => {
  const [botCompany, setBotCompany] = useState();

  const handleOnSubmit = (e) => {
    e.preventDefault();
    onUpdate(botCompany);
  };

  const handleOnUpdate = (e) => {
    setBotCompany({
      ...botCompany,
      [e.target.name]: e.target.value,
    });
  };

  useEffect(() => {
    setBotCompany(botCompanyInfo);
  }, [botCompanyInfo]);

  return (
    <Fragment>
      <Card>
        <CardHeader className="border-bottom">
          <CardTitle tag="h4">Customiza el chat</CardTitle>
        </CardHeader>
        <CardBody className="my-1 py-25">
          <Row className="gx-4">
            <Col lg="6">
              <Form onSubmit={handleOnSubmit}>
                <div className="mb-2">
                  <label htmlFor="color-select">Color del icono:</label>
                  <div className="color-picker-container d-flex gap-1 align-items-stretch">
                    <div className="mb-0">
                      <Input
                        type="text"
                        name="primaryColor"
                        onChange={handleOnUpdate}
                        value={botCompany?.primaryColor || "#500d82"}
                      />
                    </div>
                    <input
                      type="color"
                      name="primaryColor"
                      className="h-auto cursor-pointer"
                      onChange={handleOnUpdate}
                      value={botCompany?.primaryColor || "#500d82"}
                    />
                  </div>
                </div>
                <div className="mb-2">
                  <label htmlFor="color-select">Color del texto:</label>
                  <div className="color-picker-container d-flex align-items-stretch gap-1">
                    <div className="mb-0 ">
                      <Input
                        type="text"
                        name="primaryColorText"
                        onChange={handleOnUpdate}
                        value={botCompany?.primaryColorText || "#000000"}
                      />
                    </div>
                    <input
                      type="color"
                      className="h-auto cursor-pointer"
                      name="primaryColorText"
                      onChange={handleOnUpdate}
                      value={botCompany?.primaryColorText || "#00000"}
                    />
                  </div>
                </div>
                <div className="mb-2">
                  <label htmlFor="color-select">
                    Color de fondo del mensaje:
                  </label>
                  <div className="color-picker-container d-flex align-items-stretch gap-1">
                    <div className="mb-0">
                      <Input
                        type="text"
                        name="secondaryColor"
                        onChange={handleOnUpdate}
                        value={botCompany?.secondaryColor || "#F2E3FD"}
                      />
                    </div>
                    <input
                      type="color"
                      className="h-auto cursor-pointer"
                      value={botCompany?.secondaryColor || "#F2E3FD"}
                      name="secondaryColor"
                      onChange={handleOnUpdate}
                    />
                  </div>
                </div>
                <div className="mb-2">
                  <label htmlFor="color-select">
                    Color del texto del mensaje:
                  </label>
                  <div className="color-picker-container d-flex align-items-stretch gap-1">
                    <div className="mb-0">
                      <Input
                        type="text"
                        name="secondaryColorText"
                        onChange={handleOnUpdate}
                        value={botCompany?.secondaryColorText || "#F2E3FD"}
                      />
                    </div>
                    <input
                      type="color"
                      className="h-auto cursor-pointer"
                      value={botCompany?.secondaryColorText || "#F2E3FD"}
                      name="secondaryColorText"
                      onChange={handleOnUpdate}
                    />
                  </div>
                </div>
                <div className="input-container">
                  <label htmlFor="welcome-message">
                    Mensaje de bienvenida:
                  </label>
                  <Input
                    type="textarea"
                    rows="3"
                    value={botCompany?.botInitialMessage}
                    placeholder="Mensaje de bienvenida"
                    name="botInitialMessage"
                    onChange={handleOnUpdate}
                  />
                </div>
                <Col className="mt-2 pt-1" xs={12}>
                  <Button type="submit" className="me-1" color="primary">
                    Guardar
                  </Button>
                </Col>
              </Form>
            </Col>
            <Col lg="6" className="mt-2 mt-lg-0">
              <h6 className="fw-bolder mb-2 text-center">Así lucirá tu chat</h6>
              <ChatVisualization
                welcomeMessage={botCompany?.botInitialMessage}
                iconColor={botCompany?.primaryColor}
                iconTextColor={botCompany?.primaryColorText}
                backgroundMessageColor={botCompany?.secondaryColor}
                backgroundMessageTextColor={botCompany?.secondaryColorText}
              />
            </Col>
          </Row>
        </CardBody>
      </Card>
    </Fragment>
  );
};

export default PaymentMethods;
