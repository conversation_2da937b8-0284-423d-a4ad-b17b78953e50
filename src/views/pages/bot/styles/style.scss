.deleteList:hover{
    text-decoration: line-through;
    color: red;
    cursor: pointer;
    font-weight: bold;
}

.dataSourcesContent {
  box-shadow: none !important;
}

.message-bubble {
    margin-bottom: 10px;
    display: inline-block;
    margin-bottom: 8px;
    max-width: 60%;
    border-radius: 20px;
    font-size: 16px;
    line-height: 1.5;
    padding: 10px 15px;
  }
  
  .message-bubble.received {
    color: #fff;
    float: left;
  }
  
  .message-bubble.sent {
    color: #000;
    float: right;
  }
  
  .message-bubble.sent .message-text {
    text-align: right;
  }
  

  .color-picker-container{
    display: flex;
    max-width: 200px;
    justify-content: space-between;
    align-items: center;
  }
  .hex-input-container {
    margin-bottom: 12px;
    max-width: 140px;
    display: flex;
    align-items: center;
    border: solid 0.5px #b0b0b075;
    border-radius: 8px;
    padding: 6px 6px 6px 10px;
    input:focus{
      border: none !important;
    }
    input{
    max-width: 68px;
      border: none !important
    }
  }

  .input-container{
    margin-bottom: 12px;
    input{
      display: flex;
      align-items: center;
      max-width: 300px;
      border: solid 0.5px #b0b0b075;
      border-radius: 8px;
      padding: 6px 6px 6px 10px;
    }
  }
  
  .hex-prefix {
    padding-right: 5px;
    font-weight: bold;
  }
