import React from "react";
import { toast } from "react-hot-toast";
import { UncontrolledTooltip } from "reactstrap";
import { PrismLight as SyntaxHighlighter } from "react-syntax-highlighter";
import prism from "react-syntax-highlighter/dist/esm/styles/prism/prism";
import { useTranslation } from "react-i18next";

import "./styles.scss";

function DocumentationChatbot() {
  const { t } = useTranslation();
  const copyToClipboard = (text) => {
    const tempInput = document.createElement("textarea");
    tempInput.value = text;
    document.body.appendChild(tempInput);

    tempInput.select();
    document.execCommand("copy");
    document.body.removeChild(tempInput);
    toast.success("¡Script copiado al portapapeles!");
  };

  return (
    <div className="w-100">
      <p className="fw-bold">{t("bot.documentation.title")}</p>
      <SyntaxHighlighter
        language="javascript"
        id="scriptText"
        style={prism}
        className="script"
        onClick={() =>
          copyToClipboard(
            `<script src="https://chat.biitbot.com/main.js" data-secret-key="TU_LLAVE_PÚBLICA" type="module"></script>`
          )
        }
      >
        {`<script src="https://chat.biitbot.com/main.js" data-secret-key="${t(
          "bot.documentation.publicKey"
        )}" type="module"></script>`}
      </SyntaxHighlighter>

      <UncontrolledTooltip placement="top" target="scriptText">
        {t("bot.documentation.clickToCopy")}
      </UncontrolledTooltip>
      <p> {t("bot.documentation.note")}</p>
    </div>
  );
}

export default DocumentationChatbot;
