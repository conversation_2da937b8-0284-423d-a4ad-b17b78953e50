import { useRef, useState, useEffect } from "react";
import { Card } from "reactstrap";
import { FileText, User, Layers } from "react-feather";

import DataSource from "@components/datasource";
import Wizard from "@components/wizard";

import Identity from "./createBotSteps/Identity";
import Colors from "./createBotSteps/Colors";
import { getDataSources } from "../../../services/dataSources";
import { useTranslation } from "react-i18next";

const BotCreateWizard = ({ onComplete, provider = "web" }) => {
  const { t } = useTranslation();

  const ref = useRef(null);
  const [stepper, setStepper] = useState(null);
  const [dataSource, setDataSource] = useState([]);
  const [dataSourceList, setDataSourceList] = useState([]);
  const [tagsIdentity, setTagsIdentity] = useState(null);

  const handleDataSource = (data) => {
    setDataSource({
      id: data.dataSource.id,
    });
    stepper.next();
  };

  const handleIdentity = (
    name,
    description,
    identitiesSelected,
    url,
    botEnabled
  ) => {
    setTagsIdentity({
      botName: name,
      botDescription: description,
      tagsIdentity: identitiesSelected,
      allowedDomain: url,
      isBotEnabled: botEnabled,
    });

    if (provider === "web") {
      return stepper.next();
    }

    if (provider === "whatsapp" || provider === "instagram") {
      onComplete({
        dataSource,
        identity: {
          botName: name,
          botDescription: description,
          tagsIdentity: identitiesSelected,
          allowedDomain: url,
          isBotEnabled: botEnabled,
        },
      });
    }
  };

  const handleColors = async (config) => {
    onComplete({
      dataSource,
      identity: tagsIdentity,
      configuration: config,
      chatIcon: config.chatIcon,
    });
  };

  const steps = [
    {
      id: "bot-datasource",
      title: t("botCreateWizard.dataSourceStep.title"),
      subtitle: t("botCreateWizard.dataSourceStep.subtitle"),
      icon: <FileText size={18} />,
      content: (
        <DataSource data={dataSourceList} onContinue={handleDataSource} />
      ),
    },
    {
      id: "bot-identity",
      title: t("botCreateWizard.identityStep.title"),
      subtitle: t("botCreateWizard.identityStep.subtitle"),
      icon: <User size={18} />,
      content: <Identity stepper={stepper} onContinue={handleIdentity} />,
    },
  ];

  if (provider === "web") {
    steps.push({
      id: "bot-setting",
      title: t("botCreateWizard.settingStep.title"),
      subtitle: t("botCreateWizard.settingStep.subtitle"),
      icon: <Layers size={18} />,
      content: (
        <Colors
          stepper={stepper}
          onContinue={handleColors}
          botName={tagsIdentity?.name}
        />
      ),
    });
  }

  const getDataSourcesList = async () => {
    const response = await getDataSources();
    setDataSourceList(response);
  };

  useEffect(() => {
    getDataSourcesList();
  }, []);

  return (
    <div>
      <Card>
        <Wizard
          ref={ref}
          type="modern-horizontal"
          instance={(el) => setStepper(el)}
          steps={steps}
          options={{ linear: true }}
          contentWrapperClassName="dataSourcesContent border-top"
        />
      </Card>
    </div>
  );
};
export default BotCreateWizard;
