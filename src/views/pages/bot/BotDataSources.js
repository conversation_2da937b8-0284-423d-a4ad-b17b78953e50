import { useState } from "react";
import {
  CardTitle,
  Nav,
  NavItem,
  NavLink,
  TabContent,
  TabPane,
} from "reactstrap";
import DataSourcesCreate from "./createBotSteps/Datasource";
import DataSourcesUpdate from "./updateBotSteps/Datasource";
import BotText from "./BotText";

function BotDataSources({ onContinue, isUpdate = false, bot }) {
  const [active, setActive] = useState("1");

  const toggle = (tab) => {
    if (active !== tab) setActive(tab);
  };

  const handleOnContinue = (content) => {
    if (isUpdate) {
      const contentToUpdate = {
        dataSource: { id: bot.dataSource.id, content },
      };
      return onContinue(contentToUpdate);
    }

    const contentToUpdate = { botDataSource: { content } };
    onContinue(contentToUpdate);
  };

  return (
    <>
      <CardTitle>Selecciona el tipo de fuente de datos</CardTitle>
      <Nav tabs fill>
        <NavItem>
          <NavLink
            active={active === "1"}
            onClick={() => {
              toggle("1");
            }}
          >
            Archivo
          </NavLink>
        </NavItem>
        <NavItem>
          <NavLink
            active={active === "2"}
            onClick={() => {
              toggle("2");
            }}
          >
            Texto
          </NavLink>
        </NavItem>
      </Nav>
      <TabContent className="py-50" activeTab={active}>
        <TabPane tabId="1">
          {!isUpdate ? (
            <DataSourcesCreate onContinue={handleOnContinue} />
          ) : (
            <DataSourcesUpdate onUpdate={handleOnContinue} bot={bot} />
          )}
        </TabPane>
        <TabPane tabId="2">
          <BotText
            onContinue={handleOnContinue}
            isUpdate={isUpdate}
            bot={bot}
          />
        </TabPane>
      </TabContent>
    </>
  );
}

export default BotDataSources;
