import { useRef, useState, useEffect } from "react";
import { <PERSON>, Spinner } from "reactstrap";
import { FileText, User, Layers } from "react-feather";
import { toast } from "react-hot-toast";
import { useTranslation } from "react-i18next";

import Wizard from "@components/wizard";
import DataSource from "@components/datasource";

import Identity from "./updateBotSteps/Identity";
import Colors from "./updateBotSteps/Colors";
import { updateBot } from "../../../services/bot";
import UILoader from "../../../@core/components/ui-loader";
import { getDataSources } from "../../../services/dataSources";
import BotSettings from "../../../@core/components/bot-settings";

const BotUpdateWizard = ({ bot }) => {
  const ref = useRef(null);
  const { t } = useTranslation();

  const [stepper, setStepper] = useState(null);
  const [loading, setLoading] = useState(false);
  const [dataSourceList, setDataSourceList] = useState([]);

  const handleBotUpdate = async (data) => {
    setLoading(true);

    await updateBot(bot.botRequestId, "web", data)
      .then(() => {
        toast.success("Bot actualizado");
      })
      .catch(() =>
        toast.error("Error al actualizar el bot, inténtalo nuevamente")
      );

    setLoading(false);
  };

  const steps = [
    {
      id: "bot-datasource",
      title: t("botCreateWizard.dataSourceStep.title"),
      subtitle: t("botCreateWizard.dataSourceStep.subtitle"),
      icon: <FileText size={18} />,
      content: (
        <DataSource
          onContinue={handleBotUpdate}
          data={dataSourceList}
          isUpdate
          bot={bot}
        />
      ),
    },
    {
      id: "bot-identity",
      title: t("botCreateWizard.identityStep.title"),
      subtitle: t("botCreateWizard.identityStep.title"),
      icon: <User size={18} />,
      content: <Identity onContinue={handleBotUpdate} bot={bot} />,
    },
    {
      id: "bot-setting",
      title: "Integraciones con terceros",
      subtitle: "Integra to bot con otras aplicaciones",
      icon: <User size={18} />,
      content: <BotSettings bot={bot} />,
    },
  ];
  if (bot?.chatProvider === "web") {
    steps.push({
      id: "bot-setting",
      title: t("botCreateWizard.settingStep.title"),
      subtitle: t("botCreateWizard.settingStep.title"),
      icon: <Layers size={18} />,
      content: (
        <Colors stepper={stepper} onContinue={handleBotUpdate} bot={bot} />
      ),
    });
  }

  const getDataSourcesList = async () => {
    const response = await getDataSources();
    setDataSourceList(response);
  };

  useEffect(() => {
    getDataSourcesList();
  }, []);

  return (
    <div>
      <Card>
        <UILoader blocking={loading} loader={<Spinner />}>
          <Wizard
            ref={ref}
            type="modern-horizontal"
            instance={(el) => setStepper(el)}
            steps={steps}
            options={{ linear: false }}
            contentWrapperClassName="dataSourcesContent border-top"
          />
        </UILoader>
      </Card>
    </div>
  );
};
export default BotUpdateWizard;
