import { Fragment, useEffect, useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>, Col, Progress } from "reactstrap";
import { DownloadCloud } from "react-feather";
import { useDropzone } from "react-dropzone";
import { toast } from "react-hot-toast";

import {
  FILE_MAX_CHARACTERS,
  countCharacters,
} from "../../../../utility/Utils";

import "@styles/react/libs/file-uploader/file-uploader.scss";

function DataSources({ onUpdate, bot }) {
  console.log(bot);
  const chunkSize = 400;

  const [progress, setProgress] = useState(100);
  const [fileContent, setFileContent] = useState([bot?.dataSource?.content]);

  const onReadDone = () => {
    const fileData = fileContent.join("");
    const characters = countCharacters(fileData);

    if (characters > FILE_MAX_CHARACTERS) {
      toast.error(
        () => (
          <p className="mb-0">
            El archivo supera el límite de caracteres permitidos{" "}
            <span className="fw-bolder">{FILE_MAX_CHARACTERS}</span>
          </p>
        ),
        {
          style: {
            minWidth: "380px",
          },
        }
      );
      return;
    }
  };

  const { getRootProps, getInputProps } = useDropzone({
    multiple: false,
    onDrop: (files) => {
      const file = files[0];

      if (!file.name.endsWith("txt")) {
        toast.error(
          () => (
            <p className="mb-0">
              Solo puedes subir un archivo{" "}
              <span className="fw-bolder">.txt</span>
            </p>
          ),
          {
            style: {
              minWidth: "380px",
            },
          }
        );
        return;
      }

      let offset = 0;
      const chunks = [];
      const reader = new FileReader();

      function readChunk(offset) {
        const blob = file.slice(offset, chunkSize + offset);
        reader.readAsBinaryString(blob);
      }

      reader.onloadend = (evt) => {
        if (evt.target.readyState === FileReader.DONE) {
          const fileData = evt.target.result;

          chunks.push(fileData);
          offset += chunkSize;

          if (offset < file.size) {
            setTimeout(() => {
              readChunk(offset);
              setProgress((offset / file.size) * 100);
            }, 500);
          } else {
            setProgress(100);

            const fileData = chunks.join("").substring(0, FILE_MAX_CHARACTERS);
            setFileContent([fileData]);
          }
        }
      };

      readChunk(0);
    },
  });

  useEffect(() => {
    if (fileContent.length > 0) {
      onReadDone();
    }
  }, [fileContent]);

  return (
    <Fragment>
      <Col sm="12" className="mb-1">
        <Alert color="primary" className="p-2">
          <p className="mb-0">El formato admitido es (.txt).</p>
          <p className="mb-0">
            El maximo de caracteres permitidos es {FILE_MAX_CHARACTERS}{" "}
            caracteres.
          </p>
        </Alert>
        <div {...getRootProps({ className: "dropzone" })}>
          <input {...getInputProps()} />
          <div className="d-flex align-items-center justify-content-center flex-column">
            <DownloadCloud size={64} />
            <h5>Arrastre los archivos aquí o haga clic para subirlos.</h5>
          </div>
        </div>
      </Col>
      {progress > 0 && progress < 100 && (
        <div className="mt-1">
          <span>Leyendo el documento… {Math.round(progress)}%</span>
          <Progress value={progress} />
        </div>
      )}
      {progress === 100 && (
        <div className="mt-2">
          <div className="d-block">
            Cantidad de caracteres en el archivo:{" "}
            <spa className="fw-bolder">{fileContent.join("").length}.</spa>
          </div>
          <div>
            Aun puedes agregar:{" "}
            <span className="fw-bolder">
              {FILE_MAX_CHARACTERS - fileContent.join("").length} caracteres
              más.
            </span>
          </div>
        </div>
      )}
      <div className="divider">
        <div className="divider-text">
          Previsualización del contenido del archivo <br />
        </div>
      </div>
      {fileContent.join("").length > 0 && (
        <>
          <div className="mt-1">
            <div className="form-floating">
              <textarea
                className="form-control"
                style={{ height: "400px" }}
                value={fileContent.join("")}
                disabled
              ></textarea>
            </div>
          </div>
          <Alert color="warning" className="mt-1 p-1">
            <p className="mb-0">
              Revise que no se haya cortado el texto por el límite de
              caracteres.
            </p>
          </Alert>
        </>
      )}
      <div className="d-flex justify-content-end mt-1">
        <Button
          disabled={!fileContent.join("").length > 0}
          color="primary"
          onClick={() => onUpdate(fileContent.join(""))}
        >
          Actualizar bot
        </Button>
      </div>
    </Fragment>
  );
}

export default DataSources;
