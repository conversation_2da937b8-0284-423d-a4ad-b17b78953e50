import { Fragment, useEffect, useState } from "react";
import { <PERSON><PERSON>, CardTitle, Col, Form, Input, Label, Row } from "reactstrap";
import { toast } from "react-hot-toast";
import { useTranslation } from "react-i18next";

import ChatVisualization from "../../../../@core/components/chat-visualization";
import { updateBotIcon } from "../../../../services/bot";
import ImageTransformer from "../../../../@core/components/image-transformer";
import ColorPickedPreview from "../../../../@core/components/color-picket-preview";
import { Check, X } from "react-feather";

function Colors({ onContinue, bot }) {
  const { t } = useTranslation();
  const [userMessageColorBackground, setUserMessageColorBackground] = useState(
    bot.primaryColor
  );
  const [userMessageColorText, setUserMessageColorText] = useState(
    bot.primaryColorText
  );
  const [botInitialMessage, setBotInitialMessage] = useState(
    bot.botInitialMessage || t("color.welcomeMessage")
  );
  const [croppedImage, setCroppedImage] = useState(null);
  const [originalImage, setOriginalImage] = useState(null);
  const [chatOpenIconBackgroundColor, setChatOpenIconBackgroundColor] =
    useState(bot?.chatOpenIconBackgroundColor);
  const [chatOpenIconTextColor, setChatOpenIconTextColor] = useState(
    bot?.chatOpenIconTextColor
  );
  const [showWatermark, setShowWatermark] = useState(bot?.showWatermark);
  const [newIconWasSelected, setNewIconWasSelected] = useState(false);
  const [chatIconDefault, setChatIconDefault] = useState(null);

  const handleOnSubmit = (e) => {
    e.preventDefault();
    onContinue({
      configuration: {
        userMessageColorBackground,
        userMessageColorText,
        botInitialMessage,
        chatOpenIconBackgroundColor,
        chatOpenIconTextColor,
        showWatermark,
      },
    });
    if (newIconWasSelected) {
      handleImageUpload();
    }
  };

  const handleImageUpload = () => {
    fetch(croppedImage)
      .then((res) => res.blob())
      .then((blob) => {
        const fileToUpload = new File([blob], originalImage.name, {
          type: originalImage.type,
        });

        const formData = new FormData();
        formData.append("file", fileToUpload);
        formData.append("botRequestId", bot.botRequestId);

        updateBotIcon("web", formData)
          .then(() => {
            toast.success(t("iconUpdated"));
          })
          .catch((err) => {
            console.error(err);
          });
      })
      .catch((err) => {
        console.error(t("imageConversionError"), err);
      });
  };

  useEffect(() => {
    if (!bot.chatIconUrl) {
      return;
    }
    const bufferData = bot.chatIconUrl.data;
    const byteArray = new Uint8Array(bufferData);
    const blob = new Blob([byteArray], { type: "image/jpeg" });
    const imgUrl = URL.createObjectURL(blob);

    setChatIconDefault(imgUrl);
  }, [bot.chatIconUrl]);

  return (
    <Fragment>
      <CardTitle tag="h4">{t("color.customizeYourChatbot")}</CardTitle>
      <Row className="gx-4">
        <Col lg="6">
          <Form>
            <div className="input-container">
              <label htmlFor="welcome-message">
                {t("color.welcomeMessage")}:
              </label>
              <Input
                type="textarea"
                rows="3"
                value={botInitialMessage}
                name="botInitialMessage"
                onChange={(e) => setBotInitialMessage(e.target.value)}
              />
            </div>
            <div className="mb-2">
              <label htmlFor="color-select">
                {t("color.customerMessageBackgroundColor")}:
              </label>
              <div className="color-picker-container d-flex align-items-stretch gap-2">
                <div className="mb-0">
                  <Input
                    type="text"
                    name="primaryColor"
                    value={userMessageColorBackground}
                    onChange={(e) =>
                      setUserMessageColorBackground(e.target.value)
                    }
                  />
                </div>
                <input
                  type="color"
                  className="h-auto cursor-pointer"
                  value={userMessageColorBackground}
                  name="primaryColor"
                  onChange={(e) =>
                    setUserMessageColorBackground(e.target.value)
                  }
                />
              </div>
            </div>
            <div className="mb-2">
              <label htmlFor="color-select">
                {t("color.customerMessageTextColor")}:
              </label>
              <div className="color-picker-container d-flex align-items-stretch gap-1">
                <div className="mb-0">
                  <Input
                    type="text"
                    name="secondaryColorText"
                    onChange={(e) => setUserMessageColorText(e.target.value)}
                    value={userMessageColorText}
                  />
                </div>
                <input
                  type="color"
                  className="h-auto cursor-pointer"
                  value={userMessageColorText}
                  name="secondaryColorText"
                  onChange={(e) => setUserMessageColorText(e.target.value)}
                />
              </div>
            </div>
            <ImageTransformer
              title={t("color.selectAnIconForYourChatbot")}
              description={t("color.recommendAnImage")}
              defaultChatIcon={chatIconDefault}
              onComplete={(image) => setCroppedImage(image)}
              onImageSelected={(image) => {
                setOriginalImage(image);
                setNewIconWasSelected(true);
              }}
            />
            <div className="mt-1">
              <ColorPickedPreview
                title={t("color.customizeTheIconWhenTheBotIsActive")}
                onColorChange={(backgroundColor, textColor) => {
                  setChatOpenIconBackgroundColor(backgroundColor);
                  setChatOpenIconTextColor(textColor);
                }}
                initValue={{
                  chatOpenIconBackgroundColor,
                  chatOpenIconTextColor,
                }}
              />
            </div>
          </Form>
        </Col>
        <Col lg="6" className="mt-2 mt-lg-0">
          <h6 className="fw-bolder mb-2 text-center">
            {t("color.showWatermark")}
          </h6>
          <ChatVisualization
            welcomeMessage={botInitialMessage}
            backgroundMessageColor={userMessageColorBackground}
            backgroundMessageTextColor={userMessageColorText}
            botName={bot.botName}
          />
        </Col>
        <Col sm="6" className="mt-2">
          <div className="d-flex border rounded p-2">
            <div className="me-2">
              <p className="fw-bolder mb-0">{t("color.showWatermark")}</p>
            </div>
            <div className="">
              <div className="form-switch cursor-pointer">
                <Input
                  id="enable-bot"
                  type="switch"
                  checked={showWatermark}
                  onChange={(event) => setShowWatermark(event.target.checked)}
                />
                <Label
                  className="form-check-label"
                  for="enable-bot"
                  onClick={() => setShowWatermark(!showWatermark)}
                >
                  <span className="switch-icon-left">
                    <Check size={14} />
                  </span>
                  <span className="switch-icon-right">
                    <X size={14} />
                  </span>
                </Label>
              </div>
            </div>
          </div>
        </Col>
        <Col className="mt-2 pt-1" xs={12} md={12}>
          <Button
            type="button"
            onClick={handleOnSubmit}
            className="me-1"
            color="primary"
          >
            {t("color.createMyBot")}
          </Button>
        </Col>
      </Row>
    </Fragment>
  );
}

export default Colors;
