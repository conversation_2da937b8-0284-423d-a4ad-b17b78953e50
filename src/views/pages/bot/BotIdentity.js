import { Row, Col, Input, Label } from "reactstrap";
import { Check, X } from "react-feather";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";

const BotIdentity = ({ onSelected, identities = [], defaultIdentity = [] }) => {
  const [identityChecked, setIdentityChecked] = useState(defaultIdentity);
  const { t } = useTranslation();

  const handleIdentityChecked = (id, event) => {
    const isChecked = event.target.checked;

    if (isChecked) {
      const identityToSave = identities.find((identity) => identity.id === id);
      setIdentityChecked((prevState) => [...prevState, identityToSave]);
    } else {
      setIdentityChecked((prevState) =>
        prevState.filter((identity) => identity.id !== id)
      );
    }
  };

  useEffect(() => {
    onSelected(identityChecked);
  }, [identityChecked]);

  if (identities.length === 0) {
    return null;
  }

  return (
    <>
      <div className="divider">
        <div className="divider-text">
          {t("identity.identityLength", {
            identityChecked: identityChecked.length,
          })}
        </div>
      </div>
      <Row>
        {identities &&
          identities.map((item, index) => {
            return (
              <Col md="6" key={`${item.id}-${index}`}>
                <div className="d-flex mt-2">
                  <div className="d-flex align-item-center justify-content-between flex-grow-1">
                    <div className="">
                      <p className="fw-bolder mb-0">{t(item.name)}</p>
                      <span>{t(item.content)}</span>
                    </div>
                    <div className="mt-50 mt-sm-0">
                      <div className="form-switch cursor-pointer">
                        <Input
                          type="switch"
                          id={`account-${item.name}`}
                          disabled={
                            identityChecked.length === 3 &&
                            identityChecked.find(
                              (identity) => identity.id === item.id
                            )?.id !== item.id
                          }
                          checked={identityChecked.find(
                            (identity) => identity.id === item.id
                          )}
                          onChange={(event) =>
                            handleIdentityChecked(item.id, event)
                          }
                        />
                        <Label
                          className="form-check-label"
                          for={`account-${item.name}`}
                        >
                          <span className="switch-icon-left">
                            <Check size={14} />
                          </span>
                          <span className="switch-icon-right">
                            <X size={14} />
                          </span>
                        </Label>
                      </div>
                    </div>
                  </div>
                </div>
              </Col>
            );
          })}
      </Row>
    </>
  );
};

export default BotIdentity;
