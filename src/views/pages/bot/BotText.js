import { useState } from "react";
import { Col, Label, Input, Button } from "reactstrap";
import { FILE_MAX_CHARACTERS, countCharacters } from "../../../utility/Utils";

function BotText({ onContinue, isUpdate = false, bot }) {
  const [botDataSource, setBotDataSource] = useState(
    bot?.dataSource?.content || ""
  );

  return (
    <>
      <Col sm="12" className="mb-1">
        <Label className="form-label" for="botDataSource">
          Base de conocimiento para tu bot
        </Label>
        <Input
          type="textarea"
          style={{ height: "400px" }}
          value={botDataSource}
          maxLength={FILE_MAX_CHARACTERS}
          onChange={(event) => setBotDataSource(event.target.value)}
          id="botDataSource"
          placeholder="Añadir fuente de conocimiento. Todo lo que incorpore aquí será la base de las respuestas de su bot."
        />
        <div className="mt-1">
          <div className="d-block">
            Cantidad de caracteres:{" "}
            <spa className="fw-bolder">{botDataSource?.length}.</spa>
          </div>
          <div>
            Aun puedes escribir:{" "}
            <span className="fw-bolder">
              {FILE_MAX_CHARACTERS - botDataSource?.length} caracteres más.
            </span>
          </div>
        </div>
      </Col>
      <Col className="mt-2" sm="12">
        <div className="d-flex justify-content-end">
          <Button
            type="submit"
            className="me-1"
            disabled={!botDataSource}
            color="primary"
            onClick={() => onContinue(botDataSource)}
          >
            {isUpdate ? "Actualizar bot" : "Continuar"}
          </Button>
        </div>
      </Col>
    </>
  );
}

export default BotText;
