import { MoreVertical, Trash2 } from "react-feather";
import moment from "moment";
import {
  <PERSON>,
  <PERSON>,
  Card,
  Badge,
  CardBody,
  CardText,
  CardTitle,
  CardHeader,
  DropdownMenu,
  DropdownItem,
  DropdownToggle,
  UncontrolledDropdown,
} from "reactstrap";

import DocumentationChatbot from "./DocumentationChatbot";

const ApiKeysList = ({ title, instructions, data, onDeleteBot }) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle tag="h4">{title}</CardTitle>
      </CardHeader>
      <CardBody>
        <CardText>{instructions}</CardText>
        <Row className="gy-2">
          {data &&
            data.map((item, index) => (
              <Col sm={12} key={`${item.id}-${index}`}>
                <div className="bg-light-secondary position-relative rounded p-2">
                  <UncontrolledDropdown className="btn-pinned">
                    <DropdownToggle tag="span" className="cursor-pointer">
                      <MoreVertical size={18} />
                    </DropdownToggle>
                    <DropdownMenu>
                      <DropdownItem
                        className="d-flex align-items-center w-100"
                        onClick={() => onDeleteBot(item.id)}
                      >
                        <Trash2 size={14} className="me-50" />
                        <span>Borrar</span>
                      </DropdownItem>
                    </DropdownMenu>
                  </UncontrolledDropdown>
                  <div className="d-flex align-items-center flex-wrap mb-2">
                    <div className="d-flex align-items-center">
                      <h6 className="fw-bolder mb-0">
                        Tipo de bot:
                        <Badge className="" color="light-primary">
                          WEB
                        </Badge>
                      </h6>
                    </div>
                  </div>
                  <div className="d-flex align-items-center flex-wrap mb-2">
                    <div className="d-flex align-items-center">
                      <h6 className="fw-bolder mb-0">
                        Nombre del bot:
                        <Badge color="light-primary">
                          {item.botName}
                        </Badge>
                      </h6>
                    </div>
                  </div>
                  <div className="d-flex align-items-center flex-wrap mb-2">
                    <div className="d-flex align-items-center">
                      <h6 className="fw-bolder mb-0">
                        Llave publica:
                        <Badge color="light-primary">
                          {item.botRequestId}
                        </Badge>
                      </h6>
                    </div>
                  </div>
                  <div className="d-flex align-items-center flex-wrap mb-2">
                    <div className="d-flex align-items-center">
                      <h6
                        className="fw-bolder mb-0"
                        style={{ marginRight: "5px" }}
                      >
                        Fecha de creación del bot:
                      </h6>
                      <p className="p-0 m-0">
                        {moment(item.created_at).format("DD/MM/YYYY")}
                      </p>
                    </div>
                  </div>
                </div>
              </Col>
            ))}
          <DocumentationChatbot />
        </Row>
      </CardBody>
    </Card>
  );
};

export default ApiKeysList;
