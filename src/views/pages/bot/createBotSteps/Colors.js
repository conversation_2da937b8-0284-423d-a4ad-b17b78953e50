import { Fragment, useState } from "react";
import { <PERSON><PERSON>, CardTitle, Col, Form, Input, Label, Row } from "reactstrap";
import { useEffect } from "react";
import { useTranslation } from "react-i18next";

import ChatVisualization from "../../../../@core/components/chat-visualization";
import ImageTransformer from "../../../../@core/components/image-transformer";
import DefaultChatIcon from "../../../../assets/images/chat_profile.png";
import { Check, X } from "react-feather";
import ColorPickedPreview from "../../../../@core/components/color-picket-preview";

function Colors({ onContinue, botName }) {
  const { t } = useTranslation();

  const [userMessageColorBackground, setUserMessageColorBackground] =
    useState("#F2E3FD");
  const [userMessageColorText, setUserMessageColorText] = useState("#000");
  const [botInitialMessage, setBotInitialMessage] = useState("Hello, welcome");
  const [chatIcon, setChatIcon] = useState(null);
  const [originalImage, setOriginalImage] = useState(null);
  const [showWatermark, setShowWatermark] = useState(true);
  const [chatOpenIconBackgroundColor, setChatOpenIconBackgroundColor] =
    useState("#ee9300");
  const [chatOpenIconTextColor, setChatOpenIconTextColor] = useState("#ffffff");

  const handleOnSubmit = (e) => {
    e.preventDefault();
    onContinue({
      userMessageColorBackground,
      userMessageColorText,
      botInitialMessage,
      chatIcon,
      showWatermark,
      chatOpenIconBackgroundColor,
      chatOpenIconTextColor,
    });
  };

  const handleIconSelected = (image) => {
    fetch(image)
      .then((res) => res.blob())
      .then((blob) => {
        const fileToUpload = new File([blob], originalImage.name, {
          type: originalImage.type,
        });
        setChatIcon(fileToUpload);
      })
      .catch((err) => {
        console.error("Error converting cropped image to blob:", err);
      });
  };

  useEffect(() => {
    fetch(DefaultChatIcon)
      .then((res) => res.blob())
      .then((blob) => {
        const fileToUpload = new File([blob], "default-chat-icon.png", {
          type: "image/png",
        });
        setOriginalImage(fileToUpload);
        setChatIcon(fileToUpload);
      });
  }, []);

  return (
    <Fragment>
      <CardTitle tag="h4">{t("color.customizeYourChatbot")}</CardTitle>
      <Row className="gx-4">
        <Col lg="6">
          <Form>
            <div className="input-container">
              <label htmlFor="welcome-message">
                {t("color.welcomeMessage")}
              </label>
              <Input
                type="textarea"
                rows="3"
                value={botInitialMessage}
                placeholder={t("color.welcomeMessage")}
                name="welcomeMessage"
                onChange={(e) => setBotInitialMessage(e.target.value)}
              />
            </div>
            <div className="mb-2">
              <label htmlFor="color-select">
                {t("color.customerMessageBackgroundColor")}
              </label>
              <div className="color-picker-container d-flex align-items-stretch gap-1">
                <div className="mb-0">
                  <Input
                    type="text"
                    name="customerMessageBackgroundColor"
                    value={userMessageColorBackground}
                    onChange={(e) =>
                      setUserMessageColorBackground(e.target.value)
                    }
                  />
                </div>
                <input
                  type="color"
                  className="h-auto cursor-pointer"
                  value={userMessageColorBackground}
                  name="customerMessageBackgroundColor"
                  onChange={(e) =>
                    setUserMessageColorBackground(e.target.value)
                  }
                />
              </div>
            </div>
            <div className="mb-2">
              <label htmlFor="color-select">
                {t("color.customerMessageTextColor")}
              </label>
              <div className="color-picker-container d-flex align-items-stretch gap-1">
                <div className="mb-0">
                  <Input
                    type="text"
                    name="customerMessageTextColor"
                    onChange={(e) => setUserMessageColorText(e.target.value)}
                    value={userMessageColorText}
                  />
                </div>
                <input
                  type="color"
                  className="h-auto cursor-pointer"
                  value={userMessageColorText}
                  name="customerMessageTextColor"
                  onChange={(e) => setUserMessageColorText(e.target.value)}
                />
              </div>
            </div>
            <ImageTransformer
              title={t("color.selectAnIconForYourChatbot")}
              description={t("color.recommendAnImage")}
              defaultChatIcon={DefaultChatIcon}
              onComplete={(image) => handleIconSelected(image)}
              onImageSelected={(image) => setOriginalImage(image)}
            />
            <div className="mt-1">
              <ColorPickedPreview
                title={t("color.customizeTheIconWhenTheBotIsActive")}
                onColorChange={(backgroundColor, textColor) => {
                  setChatOpenIconBackgroundColor(backgroundColor);
                  setChatOpenIconTextColor(textColor);
                }}
                initValue={{
                  chatOpenIconBackgroundColor,
                  chatOpenIconTextColor,
                }}
              />
            </div>
          </Form>
        </Col>
        <Col lg="6" className="mt-2 mt-lg-0">
          <h6 className="fw-bolder mb-2 text-center">
            {t("color.customizeYourChatbot")}
          </h6>
          <ChatVisualization
            welcomeMessage={botInitialMessage}
            backgroundMessageColor={userMessageColorBackground}
            backgroundMessageTextColor={userMessageColorText}
            botName={botName}
          />
        </Col>
        <Col sm="6" className="mt-2">
          <div className="d-flex border rounded p-2">
            <div className="me-2">
              <p className="fw-bolder mb-0">{t("color.showWatermark")}</p>
            </div>
            <div className="">
              <div className="form-switch cursor-pointer">
                <Input
                  id="enable-bot"
                  type="switch"
                  checked={showWatermark}
                  onChange={(event) => setShowWatermark(event.target.checked)}
                />
                <Label
                  className="form-check-label"
                  for="enable-bot"
                  onClick={() => setShowWatermark(!showWatermark)}
                >
                  <span className="switch-icon-left">
                    <Check size={14} />
                  </span>
                  <span className="switch-icon-right">
                    <X size={14} />
                  </span>
                </Label>
              </div>
            </div>
          </div>
        </Col>
        <Col className="mt-2 pt-1 d-flex justify-content-start" xs={12} md={12}>
          <Button
            type="button"
            onClick={handleOnSubmit}
            className="me-1"
            color="primary"
          >
            {t("color.createMyBot")}
          </Button>
        </Col>
      </Row>
    </Fragment>
  );
}

export default Colors;
