import { useEffect, useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>, Col, Input, Label, Row } from "reactstrap";
import {
  BOT_DESCRIPTION_MAX_CHARACTERS,
  BOT_NAME_MAX_CHARACTERS,
  countCharacters,
  validateUrl,
} from "../../../../utility/Utils";
import BotIdentity from "../BotIdentity";
import { getTagsIdentity } from "../../../../services/generalConfig";
import { Check, X } from "react-feather";
import { toast } from "react-hot-toast";
import { useTranslation } from "react-i18next";

function Identity({ onContinue, stepper }) {
  const { t } = useTranslation();

  const [botDescription, setBotDescription] = useState("");
  const [botName, setBotName] = useState("");
  const [identities, setIdentities] = useState([]);
  const [identitiesSelected, setIdentitiesSelected] = useState([]);
  const [url, setUrl] = useState("");
  const [botEnabled, setBotEnabled] = useState(true);

  const handleOnContinue = () => {
    const element = document.getElementById("allowedDomain");
    const isUrlValid = validateUrl(url);

    if (url && !isUrlValid) {
      toast.error(t("identity.urlInvalidError"));

      element.focus();
      element.classList.add("is-invalid");
      return;
    }

    element.classList.remove("is-invalid");

    onContinue(botName, botDescription, identitiesSelected, url, botEnabled);
    stepper.next();
  };

  const handleIdentitySelection = (identitySelectedList) => {
    setIdentitiesSelected(identitySelectedList);
  };

  const getIdentityList = async () => {
    const response = await getTagsIdentity();
    setIdentities(response);
  };

  useEffect(() => {
    getIdentityList();
  }, []);

  return (
    <div>
      <Row>
        <Col sm="12" className="mb-2">
          <Label className="form-label" for="botDescription">
            {t("identity.botNameLabel")}
          </Label>
          <Input
            type="input"
            value={botName}
            maxLength={BOT_NAME_MAX_CHARACTERS}
            onChange={(event) => setBotName(event.target.value)}
            id="botName"
            placeholder={t("identity.botNamePlaceholder")}
          />
        </Col>
        <Col sm="12" className="mb-1">
          <Alert color="primary" className="p-1">
            <span className="d-block fw-bolder">
              {t("identity.descriptionExample")}
            </span>
            {t("identity.descriptionExampleContent")}
          </Alert>
          <Label className="form-label" for="botDescription">
            {t("identity.descriptionLabel")}
          </Label>
          <Input
            type="textarea"
            value={`${botDescription}`}
            onChange={(event) => setBotDescription(event.target.value)}
            id="botDescription"
            style={{ height: "200px" }}
            maxLength={BOT_DESCRIPTION_MAX_CHARACTERS}
            placeholder={t("identity.descriptionPlaceholder")}
          />
          <div className="mt-1">
            <div className="d-block">
              {t("identity.descriptionCharacterCount", {
                characterCount: countCharacters(botDescription),
              })}
            </div>
            <div>
              {t("identity.descriptionRemainingCharacters", {
                remainingCharacters:
                  BOT_DESCRIPTION_MAX_CHARACTERS -
                  countCharacters(botDescription),
              })}
            </div>
          </div>
        </Col>
        <Col>
          <BotIdentity
            onSelected={handleIdentitySelection}
            identities={identities}
          />
        </Col>
        <Col sm="12" className="mt-2">
          <div className="divider">
            <div className="divider-text">
              {t("identity.identityConfiguration")}
            </div>
          </div>
          <Col sm="6">
            <Label className="form-label" for="botDescription">
              {t("identity.urlLabel")}
            </Label>
            <Input
              type="url"
              id="allowedDomain"
              value={url}
              name="allowedDomain"
              placeholder={t("identity.urlPlaceholder")}
              onChange={(event) => setUrl(event.target.value)}
            />
            <span>
              <small className="text-muted">{t("identity.urlExample")}</small>
            </span>
          </Col>
        </Col>
        <Col sm="6" className="mt-2">
          <div className="d-flex">
            <div className="me-2">
              <p className="fw-bolder mb-0">
                {t("identity.enableChatbotLabel")}
              </p>
            </div>
            <div className="">
              <div className="form-switch cursor-pointer">
                <Input
                  id="enable-bot"
                  type="switch"
                  checked={botEnabled}
                  onChange={(event) => setBotEnabled(event.target.checked)}
                />
                <Label className="form-check-label" for="enable-bot">
                  <span className="switch-icon-left">
                    <Check size={14} />
                  </span>
                  <span className="switch-icon-right">
                    <X size={14} />
                  </span>
                </Label>
              </div>
            </div>
          </div>
        </Col>
        <div className="d-flex justify-content-end mt-1">
          <Button
            color="primary"
            disabled={
              !botDescription || !botName || !identitiesSelected.length > 0
            }
            onClick={() => handleOnContinue()}
          >
            {t("identity.continueButton")}
          </Button>
        </div>
      </Row>
    </div>
  );
}

export default Identity;
