import KnowledgeBaseHeader from "./KnowledgeBaseHeader";

import { Card, CardBody } from "reactstrap";

import "@styles/base/pages/page-knowledge-base.scss";

const KnowledgeBase = () => {
  return (
    <div className="container">
      <KnowledgeBaseHeader />
      <Card>
        <CardBody>
          <CardBody>
            <h4>Integration with instragram using Zapier</h4>
            <div>
              <h1>Connecting BitBot to Instagram via Zapier</h1>

              <h2>Prerequisites</h2>
              <ul>
                <li>
                  A Zapier account. If you don't have one, you can create a free
                  account at [Zapier](https://zapier.com).
                </li>
                <li>An active Instagram account.</li>
              </ul>

              <h2>Step 1: Sign in to Zapier</h2>
              <ol>
                <li>
                  Sign in to your Zapier account or create a new one if you
                  don't have one yet.
                </li>
                <li>
                  Once logged in, search for and select the "Instagram
                  Conversation" Zap.
                </li>
              </ol>

              <h2>Step 2: Connect your Instagram account</h2>
              <ol>
                <li>
                  After selecting the "Instagram Conversation" Zap, you'll be
                  prompted to sign in with your Instagram account.
                </li>
                <li>
                  Follow the on-screen instructions to sign in and authorize the
                  connection between Zapier and your Instagram account.
                </li>
              </ol>

              <h2>Step 3: Select the BitBot Zap</h2>
              <ol>
                <li>
                  Once you've signed in with your Instagram account, select the
                  BitBot Zap.
                </li>
                <li>
                  You'll be asked for the public key of your bot that you want
                  to connect. This public key can be found in the "Public Key"
                  column in the Bot List view on your BitBot account.
                </li>
                <li>
                  Copy and paste this public key into the corresponding field
                  and click "Continue".
                </li>
              </ol>

              <h2>Step 4: Validation and completion</h2>
              <ol>
                <li>
                  Zapier will validate the existence of the bot and the validity
                  of the public key.
                </li>
                <li>
                  Once everything is validated correctly, BitBot will be ready
                  to manage your Instagram conversations using its powerful
                  artificial intelligence.
                </li>
              </ol>

              <p>
                That's it! BitBot is now connected to your Instagram account via
                Zapier, allowing you to efficiently manage your conversations.
              </p>
            </div>
          </CardBody>
        </CardBody>
      </Card>
    </div>
  );
};

export default KnowledgeBase;
