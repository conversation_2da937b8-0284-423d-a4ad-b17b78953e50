// ** Icons Imports
import { Search } from "react-feather";

// ** Reactstrap Imports
import {
  Card,
  CardBody,
  CardText,
  Form,
  Input,
  InputGroup,
  InputGroupText,
} from "reactstrap";

const FaqFilter = ({ searchTerm, setSearchTerm, handleFilter }) => {
  const onChange = (e) => {
    if (handleFilter) {
      handleFilter(e);
    } else {
      setSearchTerm(e.target.value);
    }
  };

  return (
    <div id="knowledge-base-search">
      <Card
        className="knowledge-base-bg"
        style={{
          backgroundImage: `url('@src/assets/images/banner/banner.png')`,
        }}
      >
        <CardBody className="text-center">
          <h2 className="text-primary">Biitbot documentation</h2>
        </CardBody>
      </Card>
    </div>
  );
};

export default FaqFilter;
