.whatsappLogo {
  width: 100%;
  min-width: 52px;
  max-width: 52px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.divider {
  padding-left: 10px;
  padding-right: 10px;
  margin-bottom: 12px;
}

.container-flow {
  margin-top: 12px;
}

.block-container {
  background: #f2f2f2cc;
  border-radius: 10px;
  padding: 16px;
}

.block-completed {
  margin-left: 14px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.whatsapp-button {
  background-color: #25d366 !important;
  color: #fff !important;
  border: #fff !important;
  margin-top: 12px;
}

.whatsapp-container {
  display: flex;
  flex-direction: column;
}

.container-text {
  display: flex;
  flex-direction: column;
  justify-content: center;
}

@media (max-width: 768px) {
  .container {
    flex-direction: column;
  }

  .container-text,
  .container-qr {
    width: 100%;
  }

  .container-qr {
    margin-top: 12px;
  }

  .block-completed {
    margin-left: 0;
  }
}


.disabled-qr {
  width: 100%;
  height: 100%;
  position: absolute;
  display: flex;
  justify-content: center;
  align-items: center;
}
