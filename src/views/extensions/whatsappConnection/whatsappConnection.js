import React, { useEffect, useState } from "react";
import { <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> } from "reactstrap";

import QRGenerator from "./qrGenerator";
import WhatsappLogo from "../../../assets/logo/icons8-whatsapp-48.png";
import "./styles.scss";
import { whatsappStates } from "../../../utility/whatsappEvents";

const WhatsappConnection = ({ qrString, status, onRequestQr }) => {
  return (
    <Container fluid>
      <Row className="container-flow">
        <Col md={1} sm={2} xs={2}>
          <img
            className="whatsappLogo"
            src={WhatsappLogo}
            alt="whatsapp logo"
          />
        </Col>
        <Col md={11} sm={10} xs={10}>
          <h2>WhatsApp</h2>
          <p>Receive and reply to your messages with Biitbot</p>
        </Col>
      </Row>
      <div className="gap-1 d-flex">
        <div className="block-container w-100">
          <h2>Use WhatsApp on your computer</h2>
          <ul>
            <li>Open WhatsApp on your phone.</li>
            <li>
              Go to settings by tapping on your profile photo,
              <br /> <b>Menu</b> #iconMenu, or Settings #iconSettings.
            </li>
            <li>
              Tap <b>linked devices</b> and then <b>Link a devices</b>.
            </li>
            <li>Point your phone to this screen to capture the QR code.</li>
          </ul>
        </div>
        <div className="w-100 d-flex flex-column justify-content-center align-items-center position-relative">
          {status === whatsappStates.DISCONNECTED && (
            <div className="disabled-qr">
              <Button color="primary" outline onClick={onRequestQr}>
                Generar nuevo QR
              </Button>
            </div>
          )}

          {status !== whatsappStates.LINKED &&
            status !== whatsappStates.DISCONNECTED && (
              <>
                {!qrString && <Spinner color="black" />}
                {qrString && <QRGenerator random={qrString} />}
              </>
            )}
        </div>
      </div>
    </Container>
  );
};

export default WhatsappConnection;
