import moment from "moment";
import { MoreVertical, Trash2, Edit } from "react-feather";
import {
  DropdownItem,
  DropdownMenu,
  DropdownToggle,
  UncontrolledButtonDropdown,
  UncontrolledDropdown,
  Button
} from "reactstrap";

const getButtonColor = (status) => {
  switch (status) {
    case "pending":
      return "outline-warning";
    case "canceled":
      return "outline-danger";
    case "completed":
    case "confirmed":
      return "outline-success";
    case "preparing":
    case "in_transit":
    case "delivered":
      return "outline-info";
    default:
      return "outline-primary";
  }
};

export const orderStatus = (t) => [
  { value: "pending", label: t("order.pending") },
  { value: "confirmed", label: t("order.confirmed") },
  { value: "preparing", label: t("order.preparing") },
  { value: "in_transit", label: t("order.in_transit") },
  { value: "delivered", label: t("order.delivered") },
  { value: "canceled", label: t("order.canceled") },
];

export const orderColumn = (onStatusChange, onDelete, onEdit, t, onViewProducts) => {
  const statusOptions = orderStatus(t);
  
  return [
    {
    name: t("order.orderNumber"),
    sortable: false,
    selector: (row) => row?.orderNumber?.orderNumber,
    cell: (row) => (
      <span className="text-uppercase">
        {row?.orderNumber?.baseId}-{row?.orderNumber?.orderNumber}
      </span>
    ),
  },
  {
    name: t("order.totalAmount"),
    sortable: false,
    selector: (row) => row?.totalAmount,
    cell: (row) => <span>{row?.totalAmount}</span>,
  },
  {
    name: t("order.status"),
    sortable: false,
    selector: (row) => row?.status,
    cell: (row) => (
      <div className="position-relative rounded">
        <UncontrolledButtonDropdown>
          <DropdownToggle
            color={getButtonColor(row.status)}
            className="btn-sm"
            caret
          >
            {statusOptions.find(s => s.value === row.status)?.label || row.status}
          </DropdownToggle>
          <DropdownMenu>
            {statusOptions.map((status) => (
              <DropdownItem
                tag="button"
                key={status.value}
                onClick={() => onStatusChange(row, status.value)}
                className="d-flex align-items-center gap-1"
              >
                {status.label}
              </DropdownItem>
            ))}
          </DropdownMenu>
        </UncontrolledButtonDropdown>
      </div>
    ),
  },
  {
    name: t("order.products"),
    sortable: false,
    selector: (row) => row?.orderNumber,
    cell: (row) => (
      <Button 
        color="primary" 
        outline 
        size="sm"
        onClick={(e) => {
          e.preventDefault();
          if (onViewProducts) {
            onViewProducts(row);
          }
        }}
      >
        {t("order.seeProducts")} ({row.orderItems?.length || 0})
      </Button>
    ),
  },
  {
    name: t("general.createdAt"),
    sortable: false,
    sortField: "created_at",
    selector: (row) => row?.createdAt,
    cell: (row) => {
      // Try to parse the date with timezone information
      const date = moment.utc(row?.createdAt);
      const localDate = date.local();
      return (
        <span className="text-capitalize">
          {localDate.isValid() 
            ? localDate.format(t('dateFormats.short')) 
            : moment(row?.createdAt).format(t('dateFormats.short'))}
        </span>
      );
    },
  },
  {
    name: t("general.actions"),
    sortable: false,
    sortField: "created_at",
    selector: (row) => row?.createdAt,
    cell: (row) => (
      <UncontrolledDropdown>
        <DropdownToggle tag="div" className="btn btn-sm">
          <MoreVertical size={14} className="cursor-pointer" />
        </DropdownToggle>
        <DropdownMenu>
          <DropdownItem
            tag="a"
            href="/"
            className="w-100"
            onClick={(e) => {
              e.preventDefault();
              onEdit(row);
            }}
          >
            <Edit size={14} className="me-50" />
            <span className="align-middle">
              {t("general.edit")}
            </span>
          </DropdownItem>
          <DropdownItem
            tag="a"
            href="/"
            className="w-100"
            onClick={(e) => {
              e.preventDefault();
              onDelete(row.id);
            }}
          >
            <Trash2 size={14} className="me-50" />
            <span className="align-middle">
              {t("general.delete")}
            </span>
          </DropdownItem>
        </DropdownMenu>
      </UncontrolledDropdown>
    ),
  }
  ];
};
