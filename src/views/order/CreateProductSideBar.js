import { useEffect, useState } from "react";
import { Button, Label, Form, Input } from "reactstrap";
import { useTranslation } from "react-i18next";

import Sidebar from "@components/sidebar";
import { Check, X } from "react-feather";

const CreateProductSidebar = ({
  open,
  toggleSidebar,
  onCreate,
  defaultValue = {},
  onEdit,
}) => {
  const { t } = useTranslation();
  const isEditing = Object.keys(defaultValue)?.length > 0 ? true : false;

  const [name, setName] = useState("");
  const [description, setDescription] = useState("");
  const [price, setPrice] = useState(0);
  const [active, setActive] = useState(true);

  const onSubmit = async (e) => {
    e.preventDefault();
    if (isEditing) {
      return onEdit(defaultValue?.id, {
        name: name,
        description: description,
        price: price,
        active: active,
      });
    }
    return onCreate({
      name: name,
      description: description,
      price: price,
      active: active,
    });
  };

  const handleToggleSidebar = () => {
    toggleSidebar();
    setName("");
    setDescription("");
    setPrice(0);
    setActive(true);
  };

  useEffect(() => {
    return () => {
      setName("");
      setDescription("");
      setPrice(0);
      setActive(true);
    };
  }, []);

  useEffect(() => {
    if (defaultValue?.name) {
      setName(defaultValue.name);
    }

    if (defaultValue?.description) {
      setDescription(defaultValue.description);
    }

    if (defaultValue?.price) {
      setPrice(defaultValue.price);
    }
  }, [defaultValue]);

  return (
    <Sidebar
      size="45"
      open={open}
      title={t("product.createProduct")}
      headerClassName="mb-1"
      contentClassName="pt-0"
      toggleSidebar={handleToggleSidebar}
      onClosed={() => {
        setName("");
        setDescription("");
        setPrice(0);
        setActive(true);
      }}
    >
      <Form onSubmit={onSubmit}>
        <div className="mb-1">
          <Label className="form-label" for="name">
            {t("product.name")}
          </Label>
          <Input
            id="name"
            required
            placeholder={t("product.name")}
            value={name}
            name="name"
            onChange={(e) => setName(e.target.value)}
          />
        </div>
        <div className="mb-1">
          <Label className="form-label" for="description">
            {t("product.description")}
          </Label>
          <Input
            type="textarea"
            id="description"
            required
            placeholder={t("product.description")}
            value={description}
            name="description"
            onChange={(e) => setDescription(e.target.value)}
          />
        </div>
        <div className="mb-1">
          <Label className="form-label" for="price">
            {t("product.price")}
          </Label>
          <Input
            type="number"
            id="price"
            required
            placeholder={t("product.price")}
            value={price}
            name="price"
            onChange={(e) => setPrice(e.target.value)}
          />
        </div>

        <p className="mb-0">{t("product.activeProduct")}</p>
        <div className="form-switch cursor-pointer mb-1">
          <Input
            type="switch"
            id="toggle-ai"
            checked={active}
            onChange={(e) => setActive(e.target.checked)}
          />
          <Label className="form-check-label" for="toggle-ai">
            <span className="switch-icon-left">
              <Check size={14} />
            </span>
            <span className="switch-icon-right">
              <X size={14} />
            </span>
          </Label>
        </div>
        <Button
          type="reset"
          className="me-1"
          color="secondary"
          outline
          onClick={toggleSidebar}
        >
          {t("general.cancel")}
        </Button>
        <Button type="submit" color="primary">
          {isEditing ? t("general.edit") : t("general.create")}
        </Button>
      </Form>
    </Sidebar>
  );
};

export default CreateProductSidebar;
