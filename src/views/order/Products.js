import { useEffect, useState } from "react";
import DataTable from "react-data-table-component";
import {
  Card,
  DropdownItem,
  DropdownMenu,
  DropdownToggle,
  UncontrolledButtonDropdown,
} from "reactstrap";
import toast from "react-hot-toast";

import EmptyStateCreate from "../../@core/components/empty-state/empty-state-create";
import { productColumn } from "./productColumn";
import { useTranslation } from "react-i18next";

import { createProduct, getProducts } from "../../services/product";
import CreateProductSidebar from "./CreateProductSideBar";
import "@styles/react/libs/tables/react-dataTable-component.scss";

function Products() {
  const { t } = useTranslation();
  const [products, setProducts] = useState([]);
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);

  const handleOnDeleteOrder = () => {};
  const handleOnChangeStatus = () => {};

  const handleGetProducts = async () => {
    const response = await getProducts();
    setProducts(response);
  };

  const handleCreateProduct = async (data) => {
    await createProduct(data);
    handleGetProducts();
    toast.success(t("product.createProductSuccess"));
    setIsSidebarOpen(false);
  };

  const toggleSideBar = () => {
    setIsSidebarOpen(!isSidebarOpen);
  };

  useEffect(() => {
    handleGetProducts();
  }, []);

  return (
    <>
      <Card>
        <DataTable
          subHeader
          custom="react-data-table"
          responsive
          className="react-dataTable overflow-visible"
          columns={productColumn(handleOnDeleteOrder, handleOnChangeStatus, t)}
          subHeaderComponent={
            <div className="d-flex justify-content-between">
              <UncontrolledButtonDropdown>
                <DropdownToggle color="primary" caret>
                  {t("product.createProduct")}
                </DropdownToggle>
                <DropdownMenu>
                  <DropdownItem
                    tag="button"
                    className="d-flex align-items-center gap-1"
                    onClick={toggleSideBar}
                  >
                    {t("product.createProductManual")}
                  </DropdownItem>
                  <DropdownItem
                    tag="button"
                    disabled
                    className="d-flex align-items-center gap-1"
                    onClick={() => {}}
                  >
                    {t("product.createProductFromImage")}
                  </DropdownItem>
                </DropdownMenu>
              </UncontrolledButtonDropdown>
            </div>
          }
          data={products}
          noDataComponent={<EmptyStateCreate title={t("product.empty")} />}
        ></DataTable>
      </Card>
      <CreateProductSidebar
        open={isSidebarOpen}
        onCreate={handleCreateProduct}
        toggleSidebar={toggleSideBar}
      />
    </>
  );
}

export default Products;
