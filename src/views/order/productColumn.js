import { useState } from "react";
import moment from "moment";
import { Button } from "reactstrap";
import DescriptionModal from "./components/DescriptionModal";

export const productColumn = (onDelete, onEdit, t) => {
  const [selectedDescription, setSelectedDescription] = useState("");
  const [isDescriptionModalOpen, setIsDescriptionModalOpen] = useState(false);

  const toggleDescriptionModal = (description) => {
    setSelectedDescription(description);
    setIsDescriptionModalOpen(!isDescriptionModalOpen);
  };

  return [
    {
      name: t("product.name"),
      sortable: false,
      selector: (row) => row?.name,
      cell: (row) => <span className="text-uppercase">{row?.name}</span>,
    },
    {
      name: t("product.price"),
      sortable: false,
      selector: (row) => row?.price,
      cell: (row) => <span>{row?.price}</span>,
    },
    {
      name: t("general.active"),
      sortable: false,
      selector: (row) => row?.active,
      cell: (row) => (
        <span>{row?.active ? t("product.enabled") : t("product.disabled")}</span>
      ),
    },
    {
      name: t("product.description"),
      sortable: false,
      selector: (row) => row?.description,
      cell: (row) => (
        <div>
          <Button 
            color="primary" 
            size="sm" 
            outline
            onClick={() => toggleDescriptionModal(row?.description)}
          >
            {t('product.viewDescription')}
          </Button>
          <DescriptionModal 
            isOpen={isDescriptionModalOpen} 
            toggle={() => setIsDescriptionModalOpen(false)}
            description={selectedDescription}
          />
        </div>
      ),
    },
    {
      name: t("general.createdAt"),
      sortable: false,
      sortField: "created_at",
      selector: (row) => row?.createdAt,
      cell: (row) => (
        <span className="text-capitalize">
          {moment(row?.createdAt).format("DD/MM/YYYY")}
        </span>
      ),
    }
  ];
};
