import { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ead<PERSON>,
  <PERSON>dal<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Button,
  Form,
  Label,
  Input,
  Row,
  Col,
  Badge,
} from "reactstrap";
import { Plus, Trash2 } from "react-feather";
import { createOrder, updateOrder } from "../../../services/order";
import { getProducts } from "../../../services/product";
import toast from "react-hot-toast";

const CreateOrderModal = ({ isOpen, toggle, order, onOrderCreated }) => {
  const { t } = useTranslation();
  const [products, setProducts] = useState([{ id: "", quantity: 1 }]);
  const [isEditing, setIsEditing] = useState(false);
  
  // Inicializar el formulario con los datos de la orden si estamos editando
  useEffect(() => {
    if (order && order.products) {
      setIsEditing(true);
      // Mapear los productos al formato esperado por el formulario
      const formattedProducts = order.products.map(product => ({
        id: product.id || product.product?.id,
        quantity: product.quantity || 1,
        name: product.name || product.product?.name
      }));
      
      // Si no hay productos, agregar una fila vacía
      setProducts(formattedProducts.length > 0 ? formattedProducts : [{ id: "", quantity: 1 }]);
    } else {
      setIsEditing(false);
      setProducts([{ id: "", quantity: 1 }]);
    }
  }, [order]);
  const [availableProducts, setAvailableProducts] = useState([]);
  const [isLoading, setIsLoading] = useState(false);

  // Cargar productos disponibles
  useEffect(() => {
    const fetchProducts = async () => {
      try {
        setIsLoading(true);
        const response = await getProducts();
        if (Array.isArray(response)) {
          const formattedProducts = response.map(product => ({
            id: product.id,
            name: product.name,
            price: product.price
          }));
          setAvailableProducts(formattedProducts);
          
          // Si estamos editando, asegurarnos de que los productos de la orden estén en availableProducts
          if (isEditing && order?.products) {
            const missingProducts = order.products.filter(
              p => !formattedProducts.some(ap => ap.id === (p.id || p.product?.id))
            );
            
            if (missingProducts.length > 0) {
              const additionalProducts = missingProducts.map(p => ({
                id: p.id || p.product?.id,
                name: p.name || p.product?.name || t('order.productNotAvailable'),
                price: p.price || p.product?.price || 0
              }));
              
              setAvailableProducts(prev => [...prev, ...additionalProducts]);
            }
          }
        }
      } catch (error) {
        console.error("Error al cargar los productos:", error);
        toast.error(t('order.errorLoadingProducts'));
      } finally {
        setIsLoading(false);
      }
    };

    fetchProducts();
  }, [isEditing, order]);

  const handleAddProduct = () => {
    setProducts([...products, { id: "", quantity: 1 }]);
  };

  const handleRemoveProduct = (index) => {
    const newProducts = [...products];
    newProducts.splice(index, 1);
    setProducts(newProducts);
  };

  const handleProductChange = (index, field, value) => {
    const newProducts = [...products];
    newProducts[index] = { ...newProducts[index], [field]: value };
    setProducts(newProducts);
  };

  const calculateTotal = () => {
    return products.reduce((total, product) => {
      const productInfo = availableProducts.find(p => p.id === product.id);
      return total + (productInfo ? productInfo.price * product.quantity : 0);
    }, 0);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    // Validar que todos los productos tengan un ID y cantidad
    if (products.some(p => !p.id || p.quantity <= 0)) {
      toast.error(t('order.errorIncompleteFields'));
      return;
    }

    setIsLoading(true);
    try {
      const orderData = {
        products: products.map(p => ({
          id: p.id,
          quantity: parseInt(p.quantity, 10)
        }))
      };
      
      if (isEditing && order) {
        // Actualizar orden existente
        await updateOrder({
          id: order.id,
          ...orderData
        });
        toast.success(t('order.orderUpdated'));
      } else {
        // Crear nueva orden
        await createOrder(orderData);
        toast.success(t('order.orderCreated'));
      }
      
      onOrderCreated();
      toggle();
      // Resetear el formulario
      setProducts([{ id: "", quantity: 1 }]);
    } catch (error) {
      console.error("Error al guardar la orden:", error);
      toast.error(t('order.errorSavingOrder'));
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Modal isOpen={isOpen} toggle={toggle} size="lg">
      <ModalHeader toggle={toggle}>
        {isEditing ? t('order.editOrder') : t('order.createOrder')}
      </ModalHeader>
      <Form onSubmit={handleSubmit}>
        <ModalBody>
          {products.map((product, index) => (
              <Row key={index} className="mb-0 align-items-center">
                <Col md={6}>
                  <Label for={`product-${index}`}>{t('order.product')}</Label>
                  <Input
                    id={`product-${index}`}
                    type="select"
                    value={product.id}
                    onChange={(e) =>
                      handleProductChange(index, "id", e.target.value)
                    }
                    className="mb-2"
                    required
                  >
                    <option value="">{t('order.selectProduct')}</option>
                    {availableProducts.map((p) => (
                      <option key={p.id} value={p.id}>
                        {p.name} - ${p.price}
                      </option>
                    ))}
                  </Input>
                </Col>
                <Col md={3}>
                  <Label for={`quantity-${index}`}>{t('order.quantity')}</Label>
                  <Input
                    id={`quantity-${index}`}
                    type="number"
                    min="1"
                    value={product.quantity}
                    onChange={(e) =>
                      handleProductChange(index, "quantity", e.target.value)
                    }
                    className="mb-2"
                    required
                  />
                </Col>
              <Col md={2} className="d-flex align-items-end">
                {products.length > 1 && (
                  <Button
                    color="danger"
                    outline
                    onClick={() => handleRemoveProduct(index)}
                  >
                    <Trash2 size={16} />
                  </Button>
                )}
              </Col>
            </Row>
          ))}
          
          <div className="text-end mb-3">
            <Button
              type="button"
              color="primary"
              outline
              onClick={handleAddProduct}
              className="me-1"
            >
              <Plus size={14} className="me-25" />
              {t('order.addProduct')}
            </Button>
          </div>
          
          <div className="border-top pt-2">
            <h5 className="text-end">
              {t('order.total')}: <Badge color="primary">${calculateTotal().toFixed(2)}</Badge>
            </h5>
          </div>
        </ModalBody>
        <ModalFooter>
          <Button color="secondary" outline onClick={toggle}>
            {t('general.cancel')}
          </Button>
          <Button type="submit" color="primary" disabled={isLoading}>
            {isLoading
              ? isEditing
                ? t('order.updating')
                : t('order.creating')
              : isEditing
              ? t('order.editOrder')
              : t('order.createOrder')}
          </Button>
        </ModalFooter>
      </Form>
    </Modal>
  );
};

export default CreateOrderModal;
