import DataTable from "react-data-table-component";

import EmptyStateCreate from "../../../@core/components/empty-state/empty-state-create";
import { orderColumn } from "../orderColumn";

const CompletedOrders = ({
  orders,
  onStatusChange,
  onDeleteOrder,
  onEditOrder,
  onViewProducts,
  t
}) => {

  return (
    <DataTable
      responsive
      columns={orderColumn(onStatusChange, onDeleteOrder, onEditOrder, t, onViewProducts)}
      className="react-dataTable overflow-visible"
      data={orders}
      noDataComponent={<EmptyStateCreate title={t("order.noCompletedOrders")} />}
    />
  );
};

export default CompletedOrders;
