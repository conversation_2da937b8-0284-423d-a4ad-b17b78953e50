import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> } from 'reactstrap';
import { useTranslation } from 'react-i18next';

const DescriptionModal = ({ isOpen, toggle, description }) => {
  const { t } = useTranslation();
  
  return (
    <Modal isOpen={isOpen} toggle={toggle}>
      <ModalHeader toggle={toggle}>
        {t('product.description')}
      </ModalHeader>
      <ModalBody>
        <div style={{ whiteSpace: 'pre-line' }}>{description || t('product.noDescription')}</div>
      </ModalBody>
      <ModalFooter>
        <Button color="primary" onClick={toggle}>
          {t('general.close')}
        </Button>
      </ModalFooter>
    </Modal>
  );
};

export default DescriptionModal;
