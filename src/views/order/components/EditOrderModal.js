import { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>eader,
  <PERSON>dal<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>ton,
  Form,
  Label,
  Input,
  Row,
  Col,
  Badge,
  Spinner
} from "reactstrap";
import { Plus, Trash2 } from "react-feather";
import { updateOrder, getOrder } from "../../../services/order";
import { getProducts } from "../../../services/product";
import toast from "react-hot-toast";

const EditOrderModal = ({ isOpen, toggle, orderId, onOrderUpdated }) => {
  const { t } = useTranslation();
  const [order, setOrder] = useState(null);
  const [products, setProducts] = useState([{ id: "", quantity: 1 }]);
  const [availableProducts, setAvailableProducts] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);

  // Cargar datos de la orden y productos disponibles
  useEffect(() => {
    const fetchData = async () => {
      if (!orderId) return;
      
      setIsLoading(true);
      try {
        // Cargar orden y productos disponibles en paralelo
        const [orderResponse, productsResponse] = await Promise.all([
          getOrder(orderId),
          getProducts()
        ]);
        
        // Procesar productos disponibles
        if (Array.isArray(productsResponse)) {
          const availableProductsList = productsResponse.map(product => ({
            id: product.id,
            name: product.name,
            price: product.price
          }));
          setAvailableProducts(availableProductsList);
          
          // Establecer la orden
          setOrder(orderResponse);
          
          // Procesar productos de la orden
          let orderProducts = [];
          
          if (orderResponse.orderItems?.length > 0) {
            // Usar orderItems (nuevo formato)
            orderProducts = orderResponse.orderItems
              .filter(item => item.product) // Filtrar items sin producto
              .map(item => ({
                id: item.product.id,
                quantity: item.quantity || 1,
                name: item.product.name
              }));
          } else if (orderResponse.products?.length > 0) {
            // Usar products (formato antiguo)
            orderProducts = orderResponse.products
              .filter(p => p.id || p.product?.id) // Filtrar productos válidos
              .map(p => ({
                id: p.id || p.product.id,
                quantity: p.quantity || 1,
                name: p.name || p.product.name
              }));
          }
          
          // Si no hay productos, establecer uno vacío
          setProducts(orderProducts.length > 0 ? orderProducts : [{ id: "", quantity: 1 }]);
        }
      } catch (error) {
        console.error("Error loading order data:", error);
        toast.error("Error al cargar los datos de la orden");
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [orderId]);

  const handleAddProduct = () => {
    setProducts([...products, { id: "", quantity: 1 }]);
  };

  const handleRemoveProduct = (index) => {
    const newProducts = [...products];
    newProducts.splice(index, 1);
    setProducts(newProducts);
  };

  const handleProductChange = (index, field, value) => {
    const newProducts = [...products];
    
    // Si estamos cambiando el producto, actualizamos el nombre también
    if (field === 'id' && value) {
      const selectedProduct = availableProducts.find(p => p.id === value);
      if (selectedProduct) {
        newProducts[index] = { 
          ...newProducts[index], 
          id: value,
          name: selectedProduct.name,
          price: selectedProduct.price
        };
      } else {
        newProducts[index] = { ...newProducts[index], [field]: value };
      }
    } else {
      newProducts[index] = { ...newProducts[index], [field]: value };
    }
    
    setProducts(newProducts);
  };

  const calculateTotal = () => {
    return products.reduce((total, product) => {
      const productInfo = availableProducts.find(p => p.id === product.id) || 
                       order?.products?.find(p => (p.id === product.id || p.product?.id === product.id))?.product;
      const price = productInfo?.price || 0;
      return total + (price * (parseInt(product.quantity) || 0));
    }, 0);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    // Validar que todos los productos tengan un ID y cantidad
    if (products.some(p => !p.id || p.quantity <= 0)) {
      toast.error(t('order.errorIncompleteFields'));
      return;
    }

    setIsSaving(true);
    try {
      const orderData = {
        id: orderId,
        products: products.map(p => ({
          id: p.id,
          quantity: parseInt(p.quantity, 10)
        }))
      };
      
      await updateOrder(orderData);
      toast.success("Orden actualizada exitosamente");
      onOrderUpdated();
      toggle();
    } catch (error) {
      console.error("Error updating order:", error);
      toast.error(t('order.errorUpdatingOrder'));
    } finally {
      setIsSaving(false);
    }
  };

  if (isLoading) {
    return (
      <Modal isOpen={isOpen} toggle={toggle} size="lg">
        <ModalHeader toggle={toggle}>{t('order.loadingOrder')}</ModalHeader>
        <ModalBody className="d-flex justify-content-center align-items-center" style={{ minHeight: '200px' }}>
          <Spinner color="primary" />
        </ModalBody>
      </Modal>
    );
  }

  if (!order) {
    return (
      <Modal isOpen={isOpen} toggle={toggle} size="lg">
        <ModalHeader toggle={toggle}>{t('order.orderError')}</ModalHeader>
        <ModalBody>
          {t('order.orderLoadError')}
        </ModalBody>
        <ModalFooter>
          <Button color="secondary" onClick={toggle}>
            {t('order.close')}
          </Button>
        </ModalFooter>
      </Modal>
    );
  }

  return (
    <Modal isOpen={isOpen} toggle={toggle} size="lg">
      <ModalHeader toggle={toggle}>
        {t('order.editOrderTitle', { orderNumber: order.orderNumber?.orderNumber || order.id?.substring(0, 8) })}
      </ModalHeader>
      <Form onSubmit={handleSubmit}>
        <ModalBody>
          {products.map((product, index) => (
              <Row key={index} className="mb-3 align-items-center">
                <Col md={6}>
                  <Label for={`product-${index}`}>{t('order.product')}</Label>
                  <Input
                    id={`product-${index}`}
                    type="select"
                    value={product.id}
                    onChange={(e) =>
                      handleProductChange(index, "id", e.target.value)
                    }
                    className="mb-2"
                    required
                  >
                    <option value="">{t('order.selectProduct')}</option>
                    {availableProducts.map((p) => (
                      <option key={p.id} value={p.id}>
                        {p.name} - ${p.price}
                      </option>
                    ))}
                  </Input>
                </Col>
                <Col md={3}>
                  <Label for={`quantity-${index}`}>{t('order.quantity')}</Label>
                  <Input
                    id={`quantity-${index}`}
                    type="number"
                    min="1"
                    value={product.quantity}
                    onChange={(e) =>
                      handleProductChange(index, "quantity", e.target.value)
                    }
                    className="mb-2"
                    required
                  />
                </Col>
                <Col md={3} className="d-flex align-items-end">
                  {products.length > 1 && (
                    <Button
                      color="danger"
                      outline
                      onClick={() => handleRemoveProduct(index)}
                      className="mb-0"
                    >
                      <Trash2 size={16} />
                    </Button>
                  )}
                </Col>
              </Row>
            ))}

            <div className="text-end mb-3">
              <Button
                type="button"
                color="primary"
                outline
                onClick={handleAddProduct}
                className="me-1"
              >
                <Plus size={14} className="me-25" />
                {t('order.addProduct')}
              </Button>
            </div>
            
            <div className="border-top pt-2">
              <h5 className="text-end">
                {t('order.total')}: <Badge color="primary">${calculateTotal().toFixed(2)}</Badge>
              </h5>
            </div>
        </ModalBody>
        <ModalFooter>
          <Button color="secondary" outline onClick={toggle} disabled={isSaving}>
            {t('general.cancel')}
          </Button>
          <Button type="submit" color="primary" disabled={isSaving}>
            {isSaving ? (
              <>
                <Spinner size="sm" className="me-1" />
                {t('general.saving')}
              </>
            ) : (
              t('general.saveChanges')
            )}
          </Button>
        </ModalFooter>
      </Form>
    </Modal>
  );
};

export default EditOrderModal;
