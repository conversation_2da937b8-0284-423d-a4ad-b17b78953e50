import React from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>dal<PERSON>oot<PERSON>,
  <PERSON>,
} from "reactstrap";
import { useTranslation } from "react-i18next";
import "./ViewProductsModal.scss";

const ViewProductsModal = ({ isOpen, toggle, orderItems = [] }) => {
  const { t } = useTranslation();

  // Función para formatear el precio
  const formatPrice = (price) => {
    // Asegurarse de que el precio sea un número válido
    const numericPrice = Number(price);
    if (isNaN(numericPrice)) return 'RD$ 0.00';
    
    return new Intl.NumberFormat('es-DO', {
      style: 'currency',
      currency: 'DOP'
    }).format(numericPrice);
  };

  return (
    <Modal isOpen={isOpen} toggle={toggle} className="modal-dialog-centered" size="lg">
      <ModalHeader toggle={toggle}>
        {t("order.products")}
      </ModalHeader>
      <ModalBody>
        <Table className="products-table" hover>
          <thead>
            <tr>
              <th>{t("product.product")}</th>
              <th className="text-end">{t("product.price")}</th>
              <th className="text-center">{t("product.quantity")}</th>
              <th className="text-end">{t("product.total")}</th>
            </tr>
          </thead>
          <tbody>
            {orderItems?.length > 0 ? (
              orderItems.map((item, index) => (
                <tr key={index}>
                  <td>
                    <div className="d-flex align-items-center">
                      {item.product?.image && (
                        <img
                          src={item.product.image}
                          alt={item.product.name}
                          className="rounded me-2"
                          style={{ width: 40, height: 40, objectFit: 'cover' }}
                        />
                      )}
                      <h6 className="mb-0">{item.product?.name || 'Producto sin nombre'}</h6>
                    </div>
                  </td>

                  <td className="text-end">
                    {formatPrice(Number(item.priceAtOrder) || 0)}
                  </td>
                  <td className="text-center">
                    {Number(item.quantity) || 0}
                  </td>
                  <td className="text-end">
                    {formatPrice(Number(item.total) || 0)}
                  </td>
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan="5" className="text-center">
                  {t("product.empty")}
                </td>
              </tr>
            )}
          </tbody>
          {orderItems?.length > 0 && (
            <tfoot>
              <tr>
                <td colSpan="3" className="text-end fw-bold">
                  {t("order.totalAmount")}:
                </td>
                <td className="text-end fw-bold">
                  {formatPrice(
                    orderItems.reduce((sum, item) => {
                      const itemTotal = Number(item.total) || 0;
                      return sum + itemTotal;
                    }, 0)
                  )}
                </td>
              </tr>
            </tfoot>
          )}
        </Table>
      </ModalBody>
      <ModalFooter>
        <Button color="primary" onClick={toggle}>
          {t("general.close")}
        </Button>
      </ModalFooter>
    </Modal>
  );
};

export default ViewProductsModal;
