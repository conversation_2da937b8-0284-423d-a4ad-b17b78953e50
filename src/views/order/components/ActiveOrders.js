import DataTable from "react-data-table-component";

import EmptyStateCreate from "../../../@core/components/empty-state/empty-state-create";
import { orderColumn } from "../orderColumn";

const ActiveOrders = ({
  orders,
  onStatusChange,
  onDeleteOrder,
  onEditOrder,
  onViewProducts,
  onCreateOrder,
  t
}) => {

  return (
    <DataTable
      subHeaderComponent={
        <button className="btn btn-primary" onClick={onCreateOrder}>
          {t('order.createOrder')}
        </button>
      }
      subHeader
      responsive
      columns={orderColumn(onStatusChange, onDeleteOrder, onEditOrder, t, onViewProducts)}
      className="react-dataTable overflow-visible"
      data={orders}
      noDataComponent={<EmptyStateCreate title={t("order.noActiveOrders")} />}
    />
  );
};

export default ActiveOrders;
