import { useEffect, useState, useRef } from "react";
import { Card } from "reactstrap";
import { useTranslation } from "react-i18next";
import toast from "react-hot-toast";
import withReactContent from "sweetalert2-react-content";
import Swal from "sweetalert2";
import { FileText, CheckCircle } from "react-feather";
import Wizard from "@components/wizard";

import CreateOrderModal from "./components/CreateOrderModal";
import EditOrderModal from "./components/EditOrderModal";
import ViewProductsModal from "./components/ViewProductsModal";
import ActiveOrders from "./components/ActiveOrders";
import CompletedOrders from "./components/CompletedOrders";
import { orderStatus } from './orderColumn';
import {
  deleteOrder,
  getOrders,
  updateOrderStatus
} from "../../services/order";

function Order() {
  const { t } = useTranslation();
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [editingOrderId, setEditingOrderId] = useState(null);
  const [selectedOrder, setSelectedOrder] = useState(null);
  const [activeOrders, setActiveOrders] = useState([]);
  const [completedOrders, setCompletedOrders] = useState([]);

  const NotificationSwal = withReactContent(Swal);
  const stepper = useRef(null);
  
  // Get order items for the selected order
  const orderItems = selectedOrder?.orderItems || [];
  
  const toggleCreateModal = () => {
    setIsCreateModalOpen(!isCreateModalOpen);
  };
  
  const handleEditOrder = (order) => {
    setEditingOrderId(order.id);
  };

  const handleOrderUpdated = () => {
    setEditingOrderId(null);
    handleGetOrders();
  };

  const handleOnDeleteOrder = (orderId) => {
    return NotificationSwal.fire({
      title: t("order.deleteConfirm"),
      text: t("order.deleteConfirmDescription"),
      icon: "warning",
      showCancelButton: true,
      confirmButtonText: t("general.yesDelete"),
      customClass: {
        confirmButton: "btn btn-primary",
        cancelButton: "btn btn-outline-danger ms-1",
      },
      buttonsStyling: false,
    }).then(async (result) => {
      if (result.value) {
        await deleteOrder(orderId)
          .then(() => {
            handleGetOrders();
            return NotificationSwal.fire({
              icon: "success",
              title: t("order.deleteConfirmed"),
              customClass: {
                confirmButton: "btn btn-success",
              },
            });
          })
          .catch(() => {
            return NotificationSwal.fire({
              icon: "error",
              title: t("order.orderDeleteError"),
              text: t("order.orderDeleteErrorDescription"),
              customClass: {
                confirmButton: "btn btn-success",
              },
            });
          });
      }
    });
  };

  const handleOnChangeStatus = async (row, status) => {
    try {
      await updateOrderStatus(row.id, status);
      const statusLabel = orderStatus(t).find(s => s.value === status)?.label || status;
      toast.success(t("order.statusUpdated", { status: statusLabel }));
      handleGetOrders();
    } catch (error) {
      toast.error(t("order.statusUpdateError"));
    }
  };

  const handleGetOrders = async () => {
    const response = await getOrders();
    const completedStatuses = ['delivered', 'canceled'];
    const completedOrderToSave = response.filter(order => completedStatuses.includes(order.status));
    const activeOrderToSave = response.filter(order => !completedStatuses.includes(order.status));
    setActiveOrders(activeOrderToSave);
    setCompletedOrders(completedOrderToSave);
  };

  const handleViewProducts = (order) => {
    setSelectedOrder(order);
  };

  const toggleViewProductsModal = () => {
    setSelectedOrder(null);
  };

  const steps = [
    {
      id: 'activeOrders',
      title: t('order.activeOrders'),
      subtitle: t('order.activeOrdersSubtitle'),
      icon: <FileText size={18} />,
      content: (
        <ActiveOrders
          orders={activeOrders}
          onStatusChange={handleOnChangeStatus}
          onDeleteOrder={handleOnDeleteOrder}
          onEditOrder={handleEditOrder}
          onViewProducts={handleViewProducts}
          onCreateOrder={toggleCreateModal}
          t={t}
        />
      )
    },
    {
      id: 'completedOrders',
      title: t('order.completedOrders'),
      subtitle: t('order.completedOrdersSubtitle'),
      icon: <CheckCircle size={18} />,
      content: (
        <CompletedOrders
          orders={completedOrders}
          onStatusChange={handleOnChangeStatus}
          onDeleteOrder={handleOnDeleteOrder}
          onEditOrder={handleEditOrder}
          onViewProducts={handleViewProducts}
          t={t}
        />
      )
    }
  ];

  useEffect(() => {
    handleGetOrders();
  }, []);

  return (
    <>
      <Card>
        <Wizard
          ref={stepper}
          type="modern-horizontal"
          steps={steps}
          options={{ linear: false }}
          contentWrapperClassName="border-top"
        />
      </Card>
      
      <CreateOrderModal 
        isOpen={isCreateModalOpen} 
        toggle={toggleCreateModal}
        onOrderCreated={handleGetOrders}
      />
      
      <EditOrderModal
        isOpen={!!editingOrderId}
        toggle={() => setEditingOrderId(null)}
        orderId={editingOrderId}
        onOrderUpdated={handleOrderUpdated}
      />
      
      <ViewProductsModal
        isOpen={!!selectedOrder}
        toggle={toggleViewProductsModal}
        orderItems={orderItems}
      />
    </>
  );
}

export default Order;
