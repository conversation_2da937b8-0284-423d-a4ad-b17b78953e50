import { MoreVertical } from "react-feather";
import {
  UncontrolledDropdown,
  DropdownToggle,
  DropdownMenu,
  DropdownItem,
} from "reactstrap";
import moment from "moment";

export const column = (t, onDelete, toggleAi) => [
  {
    name: t("general.name"),
    sortable: false,
    selector: (row) => row.name,
    cell: (row) => (
      <div className="d-flex justify-content-left align-items-center">
        <div className="d-flex align-items-center gap-1 fw-bold">
          <span>{row.name || "N/A"}</span>
        </div>
      </div>
    ),
  },
  {
    name: t("general.email"),
    sortable: false,
    selector: (row) => row.email,
    cell: (row) => (
      <div className="d-flex justify-content-left align-items-center">
        <div className="d-flex align-items-center gap-1 fw-bold">
          <span>{row?.email || "N/A"}</span>
        </div>
      </div>
    ),
  },
  {
    name: t("general.contactNumber"),
    sortable: false,
    selector: (row) => row.phone,
    cell: (row) => (
      <div className="d-flex justify-content-left align-items-center">
        <div className="d-flex align-items-center gap-1 fw-bold">
          <span>{row?.phone || "N/A"}</span>
        </div>
      </div>
    ),
  },
  {
    name: t("general.AIStatus"),
    sortable: false,
    selector: (row) => row.shouldAiResponse,
    cell: (row) => (
      <div className="d-flex justify-content-left align-items-center">
        <div className="d-flex align-items-center gap-1 fw-bold">
          <span>
            {row?.shouldAiResponse
              ? t("general.AiEnabled")
              : t("general.AiDisabled")}
          </span>
        </div>
      </div>
    ),
  },
  {
    name: t("general.createdAt"),
    sortable: false,
    sortField: "created_at",
    selector: (row) => row?.created_at,
    cell: (row) => (
      <span className="text-capitalize">
        {moment(row?.createdAt).format("DD/MM/YYYY")}
      </span>
    ),
  },
  {
    name: t("general.actions"),
    minWidth: "100px",
    cell: (row) => {
      return (
        <div className="column-action">
          <UncontrolledDropdown>
            <DropdownToggle tag="div" className="btn btn-sm">
              <MoreVertical size={14} className="cursor-pointer" />
            </DropdownToggle>
            <DropdownMenu>
              <DropdownItem tag="button" onClick={() => onDelete(row.id)}>
                {t("general.delete")}
              </DropdownItem>
              <DropdownItem tag="div" onClick={() => toggleAi(row.id)}>
                {row.shouldAiResponse
                  ? t("general.disableAI")
                  : t("general.enableAI")}
              </DropdownItem>
            </DropdownMenu>
          </UncontrolledDropdown>
        </div>
      );
    },
  },
];
