import React, { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import DataTable from "react-data-table-component";
import { <PERSON><PERSON>, <PERSON> } from "reactstrap";
import Swal from "sweetalert2";
import withReactContent from "sweetalert2-react-content";
import toast from "react-hot-toast";

import { column } from "./column";
import EmptyState from "../../@core/components/empty-state/empty-state-table";
import {
  createContact,
  deleteContact,
  getAllContacts,
  toggleAIContact,
} from "../../services/contact";
import SidebarNewContact from "./sidebarNewContact";

function Contact() {
  const { t } = useTranslation();
  const NotificationSwal = withReactContent(Swal);

  const [data, setData] = useState([]);
  const [showAddContactModal, setShowAddContactModal] = useState(false);

  const handleCreateNewContact = async (contact) => {
    contact.shouldAiResponse = false;
    const response = await createContact(contact).catch((err) => err);

    if (response?.response?.data?.message === "contact_exist") {
      toast.success(t("contact.contactUpdated"));
      setShowAddContactModal(false);
      getAllContacts().then((res) => {
        setData(res);
      });
      return true;
    } else if (
      response?.code === "ERR_BAD_REQUEST" &&
      response?.response?.data?.message !== "contact_exist"
    ) {
      toast.error("contact.contactNotCreated");
      return false;
    }
    getAllContacts().then((res) => {
      setData(res);
    });
    toast.success(t("contact.contactCreated"));
    setShowAddContactModal(false);
    return true;
  };

  const handleOnDelete = async (id) => {
    return NotificationSwal.fire({
      title: t("contact.deleteConfirmationTitle"),
      icon: "warning",
      showCancelButton: true,
      confirmButtonText: t("general.yesDelete"),
      customClass: {
        confirmButton: "btn btn-primary",
        cancelButton: "btn btn-outline-danger ms-1",
      },
      buttonsStyling: false,
    }).then(async (result) => {
      if (result.value) {
        await deleteContact(id);
        toast.success(t("contact.deleteContactSuccess"));
        getAllContacts().then((res) => {
          setData(res);
        });
      }
    });
  };

  const handleToggleAi = async (value) => {
    await toggleAIContact(value)
      .then(() => {
        toast.success(t("contact.toggleAiSuccess"));
      })
      .catch(() => {
        toast.error(t("contact.toggleAiError"));
      });
    getAllContacts().then((res) => {
      setData(res);
    });
  };

  useEffect(() => {
    getAllContacts().then((res) => {
      setData(res);
    });
  }, []);

  return (
    <>
      <Card>
        <div className="react-dataTable">
          <DataTable
            pagination
            noDataComponent={
              <EmptyState
                title={t("dataSources.noDataFoundTitle")}
                message={t("dataSources.noDataFoundMessage")}
              />
            }
            subHeader
            responsive
            columns={column(t, handleOnDelete, handleToggleAi)}
            className="react-dataTable overflow-visible"
            data={data}
            subHeaderComponent={
              <div className="d-flex justify-content-between">
                <div className="position-relative">
                  <Button
                    onClick={() => setShowAddContactModal(true)}
                    color="primary"
                  >
                    {t("contact.createContact")}
                  </Button>
                </div>
              </div>
            }
          />
        </div>
      </Card>
      <SidebarNewContact
        open={showAddContactModal}
        toggleSidebar={() => setShowAddContactModal(!showAddContactModal)}
        onCreate={handleCreateNewContact}
      />
    </>
  );
}

export default Contact;
