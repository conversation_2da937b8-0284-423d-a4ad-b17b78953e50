import { useEffect, useState } from "react";
import { Button, Label, Form, Input } from "reactstrap";
import { useTranslation } from "react-i18next";

import Sidebar from "@components/sidebar";

const SidebarNewContact = ({
  open,
  toggleSidebar,
  onCreate,
  defaultValue = {},
  onEdit,
  hideInputs = [],
}) => {
  const { t } = useTranslation();
  const [name, setName] = useState(defaultValue.name || null);
  const [email, setEmail] = useState(defaultValue.email || null);
  const [phone, setPhone] = useState(defaultValue.phone || null);
  const [isWhatsapp, setIsWhatsapp] = useState(false);

  const onSubmit = async (e) => {
    e.preventDefault();
    const response = await onCreate({
      name,
      email,
      phone,
      isWhatsAppNumber: isWhatsapp,
    });
    if (response) {
      handleToggleSidebar();
    }
  };

  const handleSidebarClosed = () => {};

  const handleToggleSidebar = () => {
    toggleSidebar();
    setName("");
    setEmail();
    setPhone();
    setIsWhatsapp(false);
  };

  useEffect(() => {
    return () => {
      setName("");
      setEmail();
      setPhone();
      setIsWhatsapp(false);
    };
  }, []);

  return (
    <Sidebar
      size="45"
      open={open}
      title={t("contact.newContact")}
      headerClassName="mb-1"
      contentClassName="pt-0"
      toggleSidebar={handleToggleSidebar}
      onClosed={handleSidebarClosed}
    >
      <Form onSubmit={onSubmit}>
        <div className="mb-1">
          <Label className="form-label" for="fullName">
            {t("general.name")}
          </Label>
          <Input
            id="name"
            required
            placeholder={t("general.name")}
            value={name}
            name="name"
            onChange={(e) => setName(e.target.value)}
          />
        </div>
        <div className="mb-1">
          <Label className="form-label" for="phone">
            {t("general.contactNumber")}
          </Label>
          <Input
            id="phone"
            placeholder={t("general.contactNumber")}
            value={phone}
            name="phone"
            onChange={(e) => setPhone(e.target.value)}
          />
        </div>
        <div className="mb-1">
          <Label className="form-label" for="email">
            {t("general.email")}
          </Label>
          <Input
            id="email"
            placeholder={t("general.email")}
            value={email}
            name="email"
            onChange={(e) => setEmail(e.target.value)}
          />
        </div>
        <div className="mb-1 d-flex gap-1">
          <span>{t("general.isWhatsapp")}</span>
          <div className="d-flex gap-1">
            <Label className="form-label" for="yes">
              {t("general.yes")}
            </Label>
            <Input
              id="yes"
              type="radio"
              name="isWhatsapp"
              required
              checked={isWhatsapp === true}
              onChange={() => setIsWhatsapp(true)}
            />
          </div>
          <div className="d-flex gap-1">
            <Label className="form-label" for="no">
              {t("general.no")}
            </Label>
            <Input
              id="no"
              type="radio"
              required
              name="isWhatsapp"
              checked={isWhatsapp === false}
              onChange={() => setIsWhatsapp(false)}
            />
          </div>
        </div>

        <Button type="submit" color="primary">
          {t("general.save")}
        </Button>
      </Form>
    </Sidebar>
  );
};

export default SidebarNewContact;
