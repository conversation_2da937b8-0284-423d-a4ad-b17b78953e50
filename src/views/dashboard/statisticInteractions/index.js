import { Bar } from "react-chartjs-2";
import moment from "moment";
import { useEffect, useState } from "react";
import { <PERSON>, CardHeader, CardTitle, CardBody } from "reactstrap";
import { useTranslation } from "react-i18next";

import "chart.js/auto";
import "@styles/react/libs/flatpickr/flatpickr.scss";
import { useSkin } from "@hooks/useSkin";
import UILoader from "@components/ui-loader";

import { getInteractionsByBotRequestId } from "../../../services/statistic";

function StatisticInteractions({ botRequestId }) {
  const { t } = useTranslation();
  const { skin } = useSkin();

  const gridLineColor = "rgba(200, 200, 200, 0.2)";
  const labelColor = skin === "dark" ? "#b4b7bd" : "#6e6b7b";

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    scales: {
      x: {
        grid: {
          color: gridLineColor,
          borderColor: gridLineColor,
        },
        ticks: { color: labelColor },
      },
      y: {
        min: 0,
        grid: {
          color: gridLineColor,
          borderColor: gridLineColor,
        },
        ticks: {
          stepSize: 1,
          color: labelColor,
        },
      },
    },
    plugins: {
      legend: { display: false },
    },
  };

  const [interactions, setInteractions] = useState([]);
  const [statisticsData, setStatisticsData] = useState({
    labels: [],
    datasets: [
      {
        maxBarThickness: 15,
        borderColor: "transparent",
        borderRadius: { topRight: 15, topLeft: 15 },
        data: [],
      },
    ],
  });
  const [loading, setLoading] = useState(false);

  const createStatisticsData = () => {
    const labels = [];
    const data = [];
    interactions.forEach((interaction) => {
      labels.push(moment(interaction.date).format("DD/MM/YYYY"));
      data.push(interaction.totalInteractions);
    });
    const dataParsed = {
      labels,
      datasets: [
        {
          maxBarThickness: 15,
          backgroundColor: ["#1389BB"],
          borderColor: "transparent",
          borderRadius: { topRight: 0, topLeft: 0 },
          data,
        },
      ],
    };
    setStatisticsData(dataParsed);
  };

  const getInteractions = async () => {
    setLoading(true);
    const response = await getInteractionsByBotRequestId(botRequestId);
    setInteractions(response);
    setLoading(false);
  };

  useEffect(() => {
    if (botRequestId) {
      getInteractions();
    }
  }, [botRequestId]);

  useEffect(() => {
    if (Array.isArray(interactions) && interactions.length > 0) {
      createStatisticsData();
    }
  }, [interactions]);

  return (
    <Card>
      <UILoader blocking={loading}>
        <CardHeader className="d-flex justify-content-between align-items-sm-center align-items-start flex-sm-row flex-column">
          <CardTitle tag="h4">{t("dashboard.interactions.title")}</CardTitle>
        </CardHeader>
        <CardBody>
          <div style={{ height: "400px" }}>
            <Bar data={statisticsData} options={options} height={400} />
          </div>
        </CardBody>
      </UILoader>
    </Card>
  );
}

export default StatisticInteractions;
