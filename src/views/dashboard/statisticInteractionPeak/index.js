import { Bar } from "react-chartjs-2";
import { useEffect, useState } from "react";
import { <PERSON>, CardH<PERSON>er, CardTitle, CardBody } from "reactstrap";
import { useTranslation } from "react-i18next";

import "chart.js/auto";
import "@styles/react/libs/flatpickr/flatpickr.scss";
import UILoader from "@components/ui-loader";

import { getInteractionPeak } from "../../../services/statistic";

function StatisticInteractionPeak({ botRequestId }) {
  const { t } = useTranslation();
  const gridLineColor = "rgba(200, 200, 200, 0.2)";

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    scales: {
      x: {
        grid: {
          color: gridLineColor,
          borderColor: gridLineColor,
        },
      },
      y: {
        display: false,
      },
    },
    plugins: {
      legend: { display: false },
    },
    indexAxis: "x",
  };

  const days = {
    Lunes: 0,
    <PERSON><PERSON>: 0,
    <PERSON><PERSON><PERSON><PERSON>: 0,
    Jueves: 0,
    Viernes: 0,
    Sabado: 0,
    Domingo: 0,
  };

  const [interactions, setInteractions] = useState([]);
  const [statisticsData, setStatisticsData] = useState({
    labels: Object.keys(days),
    datasets: [],
  });
  const [loading, setLoading] = useState(false);

  const createStatisticsData = () => {
    const daysInNumber = {
      Lunes: 0,
      Martes: 1,
      Miercoles: 2,
      Jueves: 3,
      Viernes: 4,
      Sabado: 5,
      Domingo: 6,
    };
    const labels = Object.keys(days);
    const daysWithValue = days;
    const baseConfig = {
      maxBarThickness: 15,
      backgroundColor: ["#1389BB"],
      borderColor: "transparent",
      borderRadius: { topRight: 0, topLeft: 0 },
    };
    const dataParsed = [];

    Object.keys(interactions).forEach((interaction) => {
      if (interaction === "Monday") {
        daysWithValue["Lunes"] = interactions[interaction];
      } else if (interaction === "Tuesday") {
        daysWithValue["Martes"] = interactions[interaction];
      } else if (interaction === "Wednesday") {
        daysWithValue["Miercoles"] = interactions[interaction];
      } else if (interaction === "Thursday") {
        daysWithValue["Jueves"] = interactions[interaction];
      } else if (interaction === "Friday") {
        daysWithValue["Viernes"] = interactions[interaction];
      } else if (interaction === "Saturday") {
        daysWithValue["Sabado"] = interactions[interaction];
      } else if (interaction === "Sunday") {
        daysWithValue["Domingo"] = interactions[interaction];
      }
    });

    Object.keys(daysWithValue).forEach((day) => {
      const data = new Array(7).fill(0);
      data[daysInNumber[day]] =
        daysWithValue[day].value > 0 ? daysWithValue[day].value : 0;

      dataParsed.push({
        ...baseConfig,
        label: daysWithValue[day].hour,
        data,
      });
    });

    const dataset = {
      labels,
      datasets: dataParsed,
    };
    setStatisticsData(dataset);
  };

  const getInteractions = async () => {
    setLoading(true);
    const response = await getInteractionPeak(botRequestId);
    setInteractions(response);
    setLoading(false);
  };

  useEffect(() => {
    if (botRequestId) {
      getInteractions();
    }
  }, [botRequestId]);

  useEffect(() => {
    if (
      Array.isArray(Object.keys(interactions)) &&
      Object.keys(interactions).length > 0
    ) {
      createStatisticsData();
    }
  }, [interactions]);

  return (
    <Card>
      <UILoader blocking={loading}>
        <CardHeader className="d-flex justify-content-between align-items-sm-center align-items-start flex-sm-row flex-column">
          <CardTitle tag="h4">{t("dashboard.interactionPeak.title")}</CardTitle>
        </CardHeader>
        <CardBody>
          <div style={{ height: "400px" }}>
            <Bar data={statisticsData} options={options} height={400} />
          </div>
        </CardBody>
      </UILoader>
    </Card>
  );
}

export default StatisticInteractionPeak;
