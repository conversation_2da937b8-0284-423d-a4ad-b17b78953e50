import { useEffect, useState } from "react";
import { Badge, Card } from "reactstrap";
import { useTranslation } from "react-i18next";

import { getFeelingByBotRequestId } from "../../../services/statistic";

import UILoader from "@components/ui-loader";
import trialSvg from "@src/assets/images/illustration/pricing-Illustration.svg";

function StatisticFeeling({ botRequestId }) {
  const { t } = useTranslation();

  const [feeling, setFeeling] = useState("");
  const [loading, setLoading] = useState(false);

  const getFeeling = async () => {
    setLoading(true);

    const response = await getFeelingByBotRequestId(botRequestId);

    setFeeling(response?.feelingAvg);
    setLoading(false);
  };

  useEffect(() => {
    if (botRequestId) {
      getFeeling();
    }
  }, [botRequestId]);

  return (
    <Card>
      <UILoader blocking={loading}>
        <div className="pricing-trial-content d-flex justify-content-center align-items-center p-2 flex-column flex-md-row gap-2">
          {feeling && (
            <div className="text-center text-md-start mt-md-3 w-100 w-md-50 order-1 order-md-0">
              <h3>
                {t("dashboard.feeling.feelingTitle")}
                <Badge color="primary text-lowercase">
                  {" "}
                  {feeling.replace(".", "")}{" "}
                </Badge>{" "}
                {t("dashboard.feeling.feelingTitleComplement")}
              </h3>
              <h5>{t("dashboard.feeling.feelingDescription")}</h5>
            </div>
          )}
          <img
            className="pricing-trial-img img-fluid d-inline order-0 order-md-1 mb-sm-1"
            src={trialSvg}
            alt="trial svg"
            style={{
              width: "200px",
            }}
          />
        </div>
      </UILoader>
    </Card>
  );
}

export default StatisticFeeling;
