import classNames from "classnames";
import { Clock, Edit, MessageSquare } from "react-feather";
import {
  <PERSON>,
  CardBody,
  CardHeader,
  CardText,
  CardTitle,
  Col,
  Row,
} from "reactstrap";
import Avatar from "@components/avatar";
import { getStatisticStatusByBotRequestId } from "../../../services/statistic";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";

import UILoader from "@components/ui-loader";

function StatisticStatus({ botRequestId }) {
  const { t } = useTranslation();

  const [responseTimeAvg, setResponseTimeAvg] = useState(0);
  const [chatLengthAvg, setChatLengthAvg] = useState(0);
  const [messagesLengthAvg, setMessagesLengthAvg] = useState(0);
  const [loading, setLoading] = useState(false);

  const getStatistics = async () => {
    setLoading(true);
    const response = await getStatisticStatusByBotRequestId(botRequestId);
    if (response?.responseTimeAvg) {
      setResponseTimeAvg(response?.responseTimeAvg || 0);
    }
    setChatLengthAvg(response.chatLengthAvg);
    setMessagesLengthAvg(response.messagesLengthAvg);
    setLoading(false);
  };

  const cols = 3;
  const data = [
    {
      title: `${responseTimeAvg} ${t("general.seconds")}`,
      subtitle: t("dashboard.status.averageResponseTime"),
      color: "light-primary",
      icon: <Clock />,
    },
    {
      title: `${messagesLengthAvg} ${t("general.messages")}`,
      subtitle: t("dashboard.status.averageNumberOfMessagesPerUser"),
      color: "light-primary",
      icon: <Edit size={24} />,
    },
    {
      title: `${chatLengthAvg} ${t("dashboard.status.responsesGenerated")}`,
      subtitle: t("dashboard.status.averageNumberOfMessagesGeneratedByTheBot"),
      color: "light-primary",
      icon: <MessageSquare size={24} />,
    },
  ];

  const renderData = () => {
    return data.map((item, index) => {
      const colMargin = Object.keys(cols);
      const margin = index === 2 ? "sm" : colMargin[0];
      return (
        <Col
          key={index}
          {...cols}
          className={classNames({
            [`mb-2 mb-${margin}-0`]: index !== data.length - 1,
          })}
        >
          <div className="d-flex align-items-center">
            <Avatar color={item.color} icon={item.icon} className="me-2" />
            <div className="my-auto">
              <h4 className="fw-bolder mb-0">{item.title}</h4>
              <CardText className="font-small-3 mb-0">{item.subtitle}</CardText>
            </div>
          </div>
        </Col>
      );
    });
  };

  useEffect(() => {
    if (botRequestId) {
      getStatistics();
    }
  }, [botRequestId]);

  return (
    <Card className="card-statistics">
      <UILoader blocking={loading}>
        <CardHeader>
          <CardTitle tag="h4">Estadísticas promedio</CardTitle>
        </CardHeader>
        <CardBody className="statistics-body">
          <Row>{renderData()}</Row>
        </CardBody>
      </UILoader>
    </Card>
  );
}

export default StatisticStatus;
