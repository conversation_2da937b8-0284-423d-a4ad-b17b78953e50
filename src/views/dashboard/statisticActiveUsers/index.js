import { User } from "react-feather";
import { Card, CardText } from "reactstrap";
import { useTranslation } from "react-i18next";

import Avatar from "@components/avatar";
import { useEffect, useState } from "react";
import { getActiveUsers } from "../../../services/activeUser";

function StatisticActiveUsers({ botRequestId }) {
  const { t } = useTranslation();

  const [activeUsers, setActiveUsers] = useState(0);

  const getInformation = async () => {
    getActiveUsers(botRequestId).then((response) => {
      setActiveUsers(response.length);
    });
  };

  useEffect(() => {
    let getInformationInterval = null;

    if (botRequestId) {
      getInformation();

      getInformationInterval = setInterval(() => {
        getInformation();
      }, 5000);
    }

    return () => {
      clearInterval(getInformationInterval);
    };
  }, [botRequestId]);

  return (
    <Card className="p-1">
      <div className="d-flex flex-column align-items-center justify-content-center gap-2 h-100">
        <Avatar color="light-primary" icon={<User size={40} />} size="xl" />
        <div className="text-center gap-1 d-flex flex-column align-items-center justify-content-center">
          <h4 className="fw-bolder mb-0 fw-bolder font-large-2">
            {activeUsers}
          </h4>
          <CardText className="font-medium-2 mb-0">
            {t("dashboard.activeUsers.activeConversation")}
          </CardText>
        </div>
      </div>
    </Card>
  );
}

export default StatisticActiveUsers;
