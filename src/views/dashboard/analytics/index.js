import { Fragment, useEffect, useState } from "react";
import { Row, Col, Card, CardBody, Alert, Label } from "reactstrap";
import Select from "react-select";
import { toast } from "react-hot-toast";
import { useTranslation } from "react-i18next";

import "@styles/react/libs/charts/apex-charts.scss";

import StatisticStatus from "../statisticStatus";
import StatisticInteractions from "../statisticInteractions";
import StatisticInteractionPeak from "../statisticInteractionPeak";
import StatisticActiveUsers from "../statisticActiveUsers";
import StatisticFeeling from "../statisticFeeling";
import { getWebBot } from "../../../services/bot";

const BlockedFormMessage = ({ t }) => {
  return (
    <Fragment>
      <Card className="p-0">
        <CardBody className="p-0">
          <Alert color="primary" className="text-center p-2 m-0">
            {t("dashboard.needToSelectBot")}
          </Alert>
        </CardBody>
      </Card>
    </Fragment>
  );
};

const AnalyticsDashboard = () => {
  const { t } = useTranslation();

  const [selectedBot, setSelectedBot] = useState(null);
  const [bots, setBots] = useState([]);

  const getBots = async () => {
    const response = await getWebBot("web");

    if (response.botCompany.length > 0) {
      setBots(
        response.botCompany.map((bot) => ({
          value: bot.botRequestId,
          label: `${bot.botRequestId}-${bot.botName}`,
        }))
      );
    } else if (response.botCompany.length === 0) {
      toast.info(t("dashboard.noBots"));
    } else {
      toast.error(t("dashboard.errorGetBots"));
    }
  };

  const handleOnSelectBot = (bot) => {
    setSelectedBot(bot.value);
  };

  useEffect(() => {
    getBots();
  }, []);

  return (
    <>
      <Row className="mb-3">
        <Col sm={4} lg={4}>
          <div className="" style={{ zIndex: 100000 }}>
            <Label className="form-label">{t("dashboard.selectBot")}</Label>
            <Select
              isClearable={false}
              className="react-select"
              classNamePrefix="select"
              options={bots}
              placeholder={t("dashboard.selectBot")}
              onChange={(bot) => handleOnSelectBot(bot)}
              defaultValue={bots[0]}
            />
          </div>
        </Col>
      </Row>
      {selectedBot && (
        <div id="dashboard-analytics w-100">
          <Row className="match-height">
            <Col>
              <StatisticActiveUsers botRequestId={selectedBot} />
            </Col>
            <Col lg="8" sm="12">
              <StatisticFeeling botRequestId={selectedBot} />
            </Col>
          </Row>
          <Row className="match-height">
            <Col lg="12" sm="12">
              <StatisticStatus botRequestId={selectedBot} />
            </Col>
          </Row>
          <Row className="match-height">
            <Col lg="12" sm="12">
              <StatisticInteractions botRequestId={selectedBot} />
            </Col>
          </Row>
          <Row className="match-height">
            <Col lg="12" sm="12">
              <StatisticInteractionPeak botRequestId={selectedBot} />
            </Col>
          </Row>
        </div>
      )}
      {!selectedBot && <BlockedFormMessage t={t} />}
    </>
  );
};

export default AnalyticsDashboard;
