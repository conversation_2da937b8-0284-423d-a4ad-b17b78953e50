import { useState, useEffect, useCallback, useRef } from "react";
import { socket, initSocket } from "../utility/socket";
import { getCookie } from "../utility/Utils";

let accessToken = sessionStorage.getItem("accessToken");

const useInboxSocket = () => {
  const [inbox, setInbox] = useState([]);
  const [connectionStatus, setConnectionStatus] = useState("disconnected");

  // Usar useRef para mantener una referencia estable a las funciones
  const actionsRef = useRef({
    markChatAsRead: (messagesId) => {
      socket.emit("notification.message.read", { messagesId });
      socket.emit("AGENT_CONNECTED", { authToken: accessToken });
    },
    updateInbox: () => {
      socket.emit("AGENT_CONNECTED", { authToken: accessToken });
    },
    reconnect: () => {
      if (socket.connected) {
        socket.disconnect();
      }
      connectSocket();
    }
  });

  // Inicializar el token de acceso
  if (!accessToken) {
    accessToken = getCookie("accessToken");
  }

  if (!accessToken) {
    console.log("No access token found");
    return { inbox: [], connectionStatus: "disconnected", actions: actionsRef.current };
  }

  // Función para manejar la conexión manual
  const connectSocket = useCallback(() => {
    if (!socket.connected) {
      initSocket();
      setConnectionStatus("connecting");
      
      // Configurar un timeout para detectar fallos en la conexión
      const connectionTimeout = setTimeout(() => {
        if (!socket.connected) {
          setConnectionStatus("disconnected");
        }
      }, 10000); // 10 segundos de timeout
      
      return () => clearTimeout(connectionTimeout);
    }
  }, []);
  
  // Actualizar la referencia de la función de reconexión
  useEffect(() => {
    actionsRef.current.reconnect = () => {
      if (socket.connected) {
        socket.disconnect();
      }
      connectSocket();
    };
  }, [connectSocket]);

  // Efecto para la conexión inicial y reconexión cuando el componente se monta
  useEffect(() => {
    // Verificar el estado actual del socket y reconectar si es necesario
    if (!socket.connected) {
      console.log('Socket no conectado, intentando conectar...');
      connectSocket();
    } else if (connectionStatus !== 'connected') {
      // Si el socket está conectado pero el estado no lo refleja, actualizar el estado
      console.log('Socket ya está conectado, actualizando estado...');
      actionsRef.current.updateInbox();
    }
    
    // Configurar listeners de eventos
    const onConnect = () => {
      console.log('Socket connected, authenticating...');
      setConnectionStatus("connecting");
      actionsRef.current.updateInbox();
    };
    
    const onDisconnect = (reason) => {
      console.log("Socket disconnected, reason:", reason);
      setConnectionStatus("disconnected");
      
      // Intentar reconectar automáticamente si la desconexión no fue solicitada
      if (reason !== 'io client disconnect') {
        console.log('Intentando reconexión automática...');
        const reconnectTimeout = setTimeout(() => {
          connectSocket();
        }, 2000);
        
        return () => clearTimeout(reconnectTimeout);
      }
    };
    
    const onAgentConnected = (data) => {
      console.log("Agent connected, inbox data received");
      setConnectionStatus("connected");
      setInbox(Array.isArray(data) ? data : []);
    };
    
    // Configurar reconexión automática
    const handleReconnectAttempt = (attempt) => {
      console.log(`Intento de reconexión #${attempt}`);
      setConnectionStatus("connecting");
    };
    
    const handleReconnect = () => {
      console.log('Reconexión exitosa');
      setConnectionStatus("connected");
      actionsRef.current.updateInbox();
    };
    
    const handleReconnectError = (error) => {
      console.error('Error de reconexión:', error);
      setConnectionStatus("disconnected");
    };
    
    if (socket.io) {
      socket.io.on('reconnect_attempt', handleReconnectAttempt);
      socket.io.on('reconnect', handleReconnect);
      socket.io.on('reconnect_error', handleReconnectError);
    }
    
    // Configurar listeners de eventos
    socket.on("connect", onConnect);
    socket.on("disconnect", onDisconnect);
    socket.on("AGENT_CONNECTED", onAgentConnected);
    
    // Configurar el listener de nuevos mensajes
    const onNewMessage = (data) => {
      console.log("New message received:", data);
      if (data?.chatSession?.clientId) {
        socket.emit("chatHistory", { sessionId: data.chatSession.clientId });
        actionsRef.current.updateInbox();
      }
    };
    socket.on("notification.message.new", onNewMessage);
    
    // Limpieza
    return () => {
      socket.off("connect", onConnect);
      socket.off("disconnect", onDisconnect);
      socket.off("AGENT_CONNECTED", onAgentConnected);
      socket.off("notification.message.new", onNewMessage);
      
      // Limpiar listeners de reconexión
      if (socket.io) {
        socket.io.off('reconnect_attempt', handleReconnectAttempt);
        socket.io.off('reconnect', handleReconnect);
        socket.io.off('reconnect_error', handleReconnectError);
      }
    };
  }, [connectSocket, connectionStatus]); // Agregado connectionStatus a las dependencias

  // Actualizar las referencias de las funciones
  useEffect(() => {
    actionsRef.current = {
      ...actionsRef.current,
      markChatAsRead: (messagesId) => {
        socket.emit("notification.message.read", { messagesId });
        actionsRef.current.updateInbox();
      },
      updateInbox: () => {
        socket.emit("AGENT_CONNECTED", { authToken: accessToken });
      },
    };
  }, [accessToken]);

  return {
    inbox,
    connectionStatus,
    actions: actionsRef.current,
  };
};

export default useInboxSocket;
