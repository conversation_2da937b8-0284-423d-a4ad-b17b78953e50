import { useState, useEffect } from "react";
import io from "socket.io-client";

import { socket } from "../utility/socket";
import { whatsappEvents, whatsappStates } from "../utility/whatsappEvents";

const useWhatsappConnection = (serverUrl, botRequestId) => {
  const [qrString, setQrString] = useState();
  const [linkedStatus, setLinkedStatus] = useState(whatsappStates.CONNECTING);

  const requestNewQr = () => {
    socket.emit(whatsappEvents.GENERATE_QR, { botRequestId });
  };

  useEffect(() => {
    socket.on("connect_error", (error) => {
      console.error("Error de conexión:", error);
      // Puedes acceder a propiedades específicas del error, como:
      console.error("Código de error:", error.message);
      console.error("Mensaje de error:", error.description);
      if (error.message.includes("xhr poll error")) {
        console.error("Posible error de CORS");
      }
    });

    const handleListenerEvents = () => {
      socket.on(whatsappEvents.QR, ({ qr }) => {
        setQrString(qr);
      });

      socket.on(whatsappEvents.STATE, ({ state }) => {
        if (state === whatsappStates.DISCONNECTED) {
          setQrString();
        }
        setLinkedStatus(state);
      });

      socket.on(whatsappEvents.AGENT_CONNECTED, (value) => {
        if (value) {
          socket.emit(whatsappEvents.GENERATE_QR, { botRequestId });
        }
      });
    };

    if (!serverUrl || !botRequestId) {
      console.log("missing serverUrl or botRequestId or sessionId");
      socket.disconnect();
      return;
    }

    handleListenerEvents();

    socket.emit(whatsappEvents.CONNECT_AGENT, { botRequestId });

    return () => {
      socket.emit(whatsappEvents.DISCONNECTED, { botRequestId });
      socket.disconnect();
    };
  }, [serverUrl, botRequestId]);

  return {
    qrString,
    linkedStatus,
    action: {
      requestNewQr,
    },
  };
};

export default useWhatsappConnection;
