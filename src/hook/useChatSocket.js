import { useState, useEffect } from "react";
import { socket } from "../utility/socket";

const useChatSocket = (botRequestId, sessionId) => {
  const [chatHistory, setChatHistory] = useState([]);
  const [chatSocket, setChatSocket] = useState(null);
  const [chatSessionSelected, setChatSessionSelected] = useState(null);

  const handleListenerEvents = () => {
    chatSocket.on("messageSuccess", (data) => {
      setChatHistory((prevChatHistory) => [
        ...prevChatHistory,
        {
          id: new Date().getTime(),
          role: "assistant",
          content: data,
        },
      ]);
    });

    chatSocket.on("messageError", (data) => {
      console.log(data);
    });

    chatSocket.on("chatHistory", (chatSession) => {
      if (chatSessionSelected === chatSession.clientId) {
        setChatHistory(chatSession.chatResponseGenerator);
        chatSocket.emit("notification.message.read", {
          messagesId: chatSession.chatResponseGenerator.map(
            (message) => message.id
          ),
        });
      }
    });
  };

  const sendNewMessage = (message) => {
    chatSocket.emit("message", {
      message,
      botRequestId,
      sessionId,
      role: "agent",
    });
  };

  const connectSocket = () => {
    console.log("Connecting socket");
    const newChatSocket = socket;
    newChatSocket.emit("chatConnected", { sessionId });

    newChatSocket.on("chatConnected", (chatSession) => {
      setChatHistory(chatSession.chatResponseGenerator);
      setChatSocket(newChatSocket);
    });

    return newChatSocket;
  };

  useEffect(() => {
    if (!sessionId) {
      return;
    }
    setChatSessionSelected(sessionId);

    connectSocket();
  }, [sessionId]);

  useEffect(() => {
    if (!chatSocket) {
      return;
    }
    handleListenerEvents();
  }, [chatSocket]);

  return {
    chatHistory,
    sendNewMessage, // Agregamos sendNewMessage al retorno del hook
  };
};

export default useChatSocket;
