import Sidebar from "@components/sidebar";
import TextInformationSource from "./TextInformationSource";

const SidebarShowDataSource = ({
  open,
  toggleSidebar,
  content,
}) => {
  const handleSidebarClosed = () => {};

  const handleToggleSidebar = () => {
    toggleSidebar();
  };
  return (
    <Sidebar
      size="75"
      open={open}
      title="Contenido:"
      headerClassName="mb-1"
      contentClassName="pt-0"
      toggleSidebar={handleToggleSidebar}
      onClosed={handleSidebarClosed}
    >
      <TextInformationSource
      content={content}
      />
    </Sidebar>
  );
};

export default SidebarShowDataSource;
