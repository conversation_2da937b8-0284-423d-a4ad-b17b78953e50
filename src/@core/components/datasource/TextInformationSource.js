import { Col, Label, Input } from "reactstrap";

import { FILE_MAX_CHARACTERS } from "../../../utility/Utils";

function TextInformationSource({ 
    content 
}) {

  return (
    <>
      <Col sm="3" className="mb-1">
        <Label className="form-label" for="botDataSource">
          Nombre de tu fuente de datos
        </Label>
        <Input
          type="input"
          value={content.name}
        disabled  
        id="dataSourceName"
          placeholder="Dale un nombre a tu fuente de datos"
        />
      </Col>
      <Col sm="12" className="mb-1">
        <Label className="form-label" for="botDataSource">
        Contenido de la fuente de datos
        </Label>
        <Input
          type="textarea"
          style={{ height: "400px" }}
          value={content.text}
          maxLength={FILE_MAX_CHARACTERS}
          disabled
          id="botDataSource"
          placeholder="Añadir fuente de conocimiento. Todo lo que incorpore aquí será la base de las respuestas de su bot."
        />
      </Col>
    </>
  );
}

export default TextInformationSource;
