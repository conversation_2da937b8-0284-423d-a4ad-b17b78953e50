import { Fragment, useEffect, useState } from "react";
import { <PERSON><PERSON>, But<PERSON>, Col, Input, Label, Progress } from "reactstrap";
import { DownloadCloud } from "react-feather";
import { useDropzone } from "react-dropzone";
import { toast } from "react-hot-toast";
import { useTranslation } from "react-i18next";

import { FILE_MAX_CHARACTERS, countCharacters } from "../../../utility/Utils";

import "@styles/react/libs/file-uploader/file-uploader.scss";

function DataSources({ onContinue, dataSource, isUpdate = false }) {
  const chunkSize = 400;
  const { t } = useTranslation();
  const [progress, setProgress] = useState(0);
  const [fileContent, setFileContent] = useState([]);
  const [name, setName] = useState(dataSource?.name || "");
  const [fileName, setFileName] = useState(null);

  const closeButtonStyle = {
    borderRadius: "100%",
    color: "red",
    paddingRight: "12px",
  };
  const onReadDone = () => {
    const fileData = fileContent.join("");
    const characters = countCharacters(fileData);

    if (characters > FILE_MAX_CHARACTERS) {
      toast.error(
        () => (
          <p className="mb-0">
            El archivo supera el límite de caracteres permitidos{" "}
            <span className="fw-bolder">{FILE_MAX_CHARACTERS}</span>
          </p>
        ),
        {
          style: {
            minWidth: "380px",
          },
        }
      );
      return;
    }
  };

  const deleteArchive = () => {
    setFileName(null);
    setFileContent([]);
  };

  const { getRootProps, getInputProps } = useDropzone({
    multiple: false,
    onDrop: (files) => {
      const file = files[0];
      setFileName(file.path);

      if (!file.name.endsWith("txt")) {
        toast.error(
          () => (
            <p className="mb-0">
              Solo puedes subir un archivo{" "}
              <span className="fw-bolder">.txt</span>
            </p>
          ),
          {
            style: {
              minWidth: "380px",
            },
          }
        );
        return;
      }

      let offset = 0;
      const chunks = [];
      const reader = new FileReader();

      function readChunk(offset) {
        const slice = file.slice(offset, offset + chunkSize);
        reader.readAsText(slice);
      }

      reader.onloadend = (evt) => {
        if (evt.target.readyState === FileReader.DONE) {
          const fileData = evt.target.result;

          chunks.push(fileData);
          offset += chunkSize;

          if (offset < file.size) {
            setTimeout(() => {
              readChunk(offset);
              setProgress((offset / file.size) * 100);
            }, 500);
          } else {
            setProgress(100);

            const fileData = chunks.join("").substring(0, FILE_MAX_CHARACTERS);
            console.log(fileData);
            setFileContent([fileData]);
          }
        }
      };

      readChunk(0);
    },
  });

  const handleOnContinue = () => {
    onContinue({
      name,
      content: fileContent.join(""),
    });
  };

  useEffect(() => {
    if (fileContent.length > 0) {
      onReadDone();
    }
  }, [fileContent]);

  return (
    <Fragment>
      <Col sm="3" className="mb-1">
        <Label className="form-label" for="botDataSource">
          {t("fileDataSource.dataSourceNameLabel")}
        </Label>
        <Input
          type="input"
          value={name}
          onChange={(event) => setName(event.target.value)}
          id="dataSourceName"
          placeholder={t("fileDataSource.dataSourceNamePlaceholder")}
        />
      </Col>
      <Col sm="12" className="mb-1">
        <Alert color="primary" className="p-2">
          <p className="mb-0">{t("fileDataSource.fileFormatInfo")}</p>
          <p className="mb-0">
            {t("fileDataSource.maxCharactersInfo")} {FILE_MAX_CHARACTERS}
          </p>
        </Alert>
        <div {...getRootProps({ className: "dropzone" })}>
          <input {...getInputProps()} />
          <div className="d-flex align-items-center justify-content-center flex-column">
            {fileName ? (
              <>
                <div>
                  {t("fileDataSource.fileLabel")}: {fileName}
                  <span
                    style={closeButtonStyle}
                    onClick={() => {
                      deleteArchive();
                    }}
                  >
                    {t("fileDataSource.deleteButtonLabel")}
                  </span>
                </div>
              </>
            ) : (
              <>
                <DownloadCloud size={64} />
                <h5>{t("fileDataSource.fileDropzoneMessage")}</h5>
              </>
            )}
          </div>
        </div>
      </Col>
      {progress > 0 && progress < 100 && (
        <div className="mt-1">
          <span>{t("fileDataSource.readingDocumentProgress")}</span>
          <Progress value={progress} />
        </div>
      )}
      {progress === 100 && (
        <div className="mt-2">
          <div className="d-block">
            {t("fileDataSource.characterCountInfo")}{" "}
            <span className="fw-bolder">
              {fileContent.join("").length} {t("general.characters")}.
            </span>
          </div>
          <div>
            {t("fileDataSource.remainingCharactersInfo")}:{" "}
            <span className="fw-bolder">
              {FILE_MAX_CHARACTERS - fileContent.join("").length}{" "}
              {t("general.characters")}
            </span>
          </div>
        </div>
      )}
      {fileContent.join("").length > 0 ||
        (dataSource?.content?.length > 0 && (
          <>
            <div className="divider">
              <div className="divider-text">
                {t("fileDataSource.previewDividerText")} <br />
              </div>
            </div>
            <div className="mt-1">
              <div className="form-floating">
                <textarea
                  className="form-control"
                  style={{ height: "400px" }}
                  value={fileContent.join("") || dataSource?.content}
                  disabled
                ></textarea>
              </div>
            </div>
            <Alert color="warning" className="mt-1 p-1">
              <p className="mb-0">{t("fileDataSource.textCutoffWarning")}</p>
            </Alert>
          </>
        ))}
      <div className="d-flex justify-content-end mt-1">
        <Button
          disabled={!isUpdate && !fileContent.join("").length > 0}
          color="primary"
          onClick={handleOnContinue}
        >
          {isUpdate
            ? t("fileDataSource.updateButtonLabel")
            : t("fileDataSource.createButtonLabel")}
        </Button>
      </div>
    </Fragment>
  );
}

export default DataSources;
