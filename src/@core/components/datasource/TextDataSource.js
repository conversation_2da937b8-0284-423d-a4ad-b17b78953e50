import { useState } from "react";
import { Col, Label, Input, Button } from "reactstrap";

import { FILE_MAX_CHARACTERS } from "../../../utility/Utils";

function TextDataSource({ onContinue, isUpdate = false, dataSource }) {
  const [botDataSource, setBotDataSource] = useState(dataSource?.content || "");
  const [name, setName] = useState(dataSource?.name || "");

  return (
    <>
      <Col sm="3" className="mb-1">
        <Label className="form-label" for="botDataSource">
          Nombre de tu fuente de datos
        </Label>
        <Input
          type="input"
          value={name}
          onChange={(event) => setName(event.target.value)}
          id="dataSourceName"
          placeholder="Dale un nombre a tu fuente de datos"
        />
      </Col>
      <Col sm="12" className="mb-1">
        <Label className="form-label" for="botDataSource">
          Base de conocimiento para tu bot
        </Label>
        <Input
          type="textarea"
          style={{ height: "400px" }}
          value={botDataSource}
          maxLength={FILE_MAX_CHARACTERS}
          onChange={(event) => setBotDataSource(event.target.value)}
          id="botDataSource"
          placeholder="Añadir fuente de conocimiento. Todo lo que incorpore aquí será la base de las respuestas de su bot."
        />
        <div className="mt-1">
          <div className="d-block">
            Cantidad de caracteres:{" "}
            <spa className="fw-bolder">{botDataSource?.length}.</spa>
          </div>
          <div>
            Aun puedes escribir:{" "}
            <span className="fw-bolder">
              {FILE_MAX_CHARACTERS - botDataSource?.length} caracteres más.
            </span>
          </div>
        </div>
      </Col>
      <Col className="mt-2" sm="12">
        <div className="d-flex justify-content-end">
          <Button
            type="submit"
            className="me-1"
            disabled={(!botDataSource || !name) && !isUpdate}
            color="primary"
            onClick={() => onContinue({ name, content: botDataSource })}
          >
            {isUpdate ? "Actualizar fuente de datos" : "Crear fuente de datos"}
          </Button>
        </div>
      </Col>
    </>
  );
}

export default TextDataSource;
