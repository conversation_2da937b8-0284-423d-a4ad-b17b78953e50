import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import DataTable from "react-data-table-component";
import {
  <PERSON>ton,
  Card,
  CardBody,
  CardHeader,
  CardTitle,
  Col,
  Row,
} from "reactstrap";
import Swal from "sweetalert2";
import withReactContent from "sweetalert2-react-content";

import { column } from "./column";
import SidebarCreateDataSource from "./SidebarCreateDataSource";
import {
  createDataSource,
  deleteDataSource,
  updateDataSource,
} from "../../../services/dataSources";
import EmptyState from "../empty-state/empty-state-table";
import SidebarShowDataSource from "./SidebarShowDataSource";

function DataSourceUpdate({ bot, onChangeSelect }) {
  const { t } = useTranslation();

  return (
    <>
      <Row>
        <Col md="6" sm="6">
          <Card className="border">
            <CardHeader className="border-bottom">
              <CardTitle tag="h4">{t("dataSources.dataSourceInUse")}</CardTitle>
            </CardHeader>
            <CardBody className="my-2 py-25">
              <p className="fw-bolder">{t("dataSources.dataSourceContent")}</p>
              <small>{bot.dataSource?.content}</small>
            </CardBody>
          </Card>
        </Col>
      </Row>
      <Button color="primary" onClick={onChangeSelect}>
        {t("dataSources.changeDataSource")}
      </Button>
    </>
  );
}

function DataSource({
  data = [],
  onContinue,
  isUpdate = false,
  bot,
  showSelect = true,
  showCreateButton = false,
}) {
  const { t } = useTranslation();
  const [enableUpdate, setEnableUpdate] = useState(isUpdate);
  const [currentBot, setCurrentBot] = useState(bot);
  const [showSidebar, setShowSidebar] = useState(false);
  const [showInformation, setShowInformation] = useState(false);
  const [information, setInformation] = useState(null);
  const [selectedDataSourceForEdit, setSelectedDataSourceForEdit] =
    useState(null);

  const NotificationSwal = withReactContent(Swal);

  const handleDataSourceSelected = (dataSource) => {
    onContinue({ dataSource });
    setCurrentBot({
      ...currentBot,
      dataSource,
    });
    setEnableUpdate(true);
  };

  const handleDataSourceCreated = async (data) => {
    if (selectedDataSourceForEdit) {
      await updateDataSource(data.dataSource);
      NotificationSwal.fire({
        title: t("dataSources.dataSourceUpdatedTitle"),
        text: t("dataSources.dataSourceUpdatedMessage"),
        icon: "success",
        showCancelButton: false,
        confirmButtonText: "Ok",
        customClass: {
          confirmButton: "btn btn-primary",
        },
        buttonsStyling: false,
      });
    } else {
      await createDataSource(data.dataSource);
      NotificationSwal.fire({
        title: t("dataSources.dataSourceCreatedTitle"),
        text: t("dataSources.dataSourceCreatedMessage"),
        icon: "success",
        showCancelButton: false,
        confirmButtonText: "Ok",
        customClass: {
          confirmButton: "btn btn-primary",
        },
        buttonsStyling: false,
      });
    }
    onContinue();
    setShowSidebar(!showSidebar);
  };

  const handleDelete = async (dataSourceId) => {
    const response = await deleteDataSource(dataSourceId);
    if (response?.message === "Data source is in use") {
      NotificationSwal.fire({
        title: t("dataSourceInUseErrorTitle"),
        text: t("dataSourceInUseErrorMessage"),
        icon: "error",
        showCancelButton: false,
        confirmButtonText: "Ok",
        customClass: {
          confirmButton: "btn btn-primary",
        },
        buttonsStyling: false,
      });
    } else {
      NotificationSwal.fire({
        title: t("dataSources.dataSourceDeletedTitle"),
        text: t("dataSources.dataSourceDeletedMessage"),
        icon: "success",
        showCancelButton: false,
        confirmButtonText: "Ok",
        customClass: {
          confirmButton: "btn btn-primary",
        },
        buttonsStyling: false,
      });
    }
    onContinue();
  };

  const handleOnEdit = (data) => {
    setSelectedDataSourceForEdit(data);
    setShowSidebar(true);
  };

  const handleShowContent = (content, data) => {
    setShowInformation(!showInformation);
    setSelectedDataSourceForEdit(data);
    setInformation({ text: content, name: data.name });
  };

  if (enableUpdate) {
    return (
      <DataSourceUpdate
        bot={currentBot}
        onChangeSelect={() => setEnableUpdate(false)}
      />
    );
  }

  return (
    <Card>
      <div className="react-dataTable">
        <DataTable
          pagination
          noDataComponent={
            <EmptyState
              title={t("dataSources.noDataFoundTitle")}
              message={t("dataSources.noDataFoundMessage")}
            />
          }
          subHeader={showCreateButton}
          responsive
          columns={column(
            showSelect,
            handleDelete,
            handleOnEdit,
            handleDataSourceSelected,
            handleShowContent,
            t
          )}
          className="react-dataTable overflow-visible"
          data={data.reverse()}
          subHeaderComponent={
            <div className="d-flex justify-content-between">
              <div className="position-relative">
                <Button
                  onClick={() => {
                    setSelectedDataSourceForEdit(null);
                    setShowSidebar(!showSidebar);
                  }}
                  color="primary"
                >
                  {t("dataSources.createDataSource")}
                </Button>
              </div>
            </div>
          }
        />
      </div>
      <SidebarCreateDataSource
        open={showSidebar}
        toggleSidebar={() => setShowSidebar(!showSidebar)}
        onCreate={handleDataSourceCreated}
        isUpdate={selectedDataSourceForEdit}
        bot={selectedDataSourceForEdit}
      />
      <SidebarShowDataSource
        open={showInformation}
        toggleSidebar={() => setShowInformation(!showInformation)}
        content={information}
      />
    </Card>
  );
}

export default DataSource;
