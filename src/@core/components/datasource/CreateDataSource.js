import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import {
  CardTitle,
  Nav,
  NavItem,
  NavLink,
  TabContent,
  TabPane,
} from "reactstrap";

import FileDataSource from "./FileDataSource";
import TextDataSource from "./TextDataSource";

function CreateDataSource({ onContinue, isUpdate = false, bot }) {
  const { t } = useTranslation();
  const [active, setActive] = useState("1");

  const toggle = (tab) => {
    if (active !== tab) setActive(tab);
  };

  const handleOnContinue = (dataSource) => {
    if (isUpdate) {
      const contentToUpdate = {
        dataSource: { id: bot.id, ...dataSource },
      };
      return onContinue(contentToUpdate);
    }

    const contentToUpdate = { dataSource };
    onContinue(contentToUpdate);
  };

  return (
    <>
      <CardTitle>{t("createDataSource.selectDataSourceType")}</CardTitle>
      <Nav tabs fill>
        <NavItem>
          <NavLink
            active={active === "1"}
            onClick={() => {
              toggle("1");
            }}
          >
            {t("createDataSource.fileTab")}
          </NavLink>
        </NavItem>
        <NavItem>
          <NavLink
            active={active === "2"}
            onClick={() => {
              toggle("2");
            }}
          >
            {t("createDataSource.textTab")}
          </NavLink>
        </NavItem>
      </Nav>
      <TabContent className="py-50" activeTab={active}>
        <TabPane tabId="1">
          <FileDataSource
            onContinue={handleOnContinue}
            isUpdate={isUpdate}
            dataSource={bot}
          />
        </TabPane>
        <TabPane tabId="2">
          <TextDataSource
            onContinue={handleOnContinue}
            isUpdate={isUpdate}
            dataSource={bot}
          />
        </TabPane>
      </TabContent>
    </>
  );
}

export default CreateDataSource;
