import { <PERSON><PERSON><PERSON><PERSON>, Trash2, Edit2, Eye } from "react-feather";
import {
  UncontrolledDropdown,
  DropdownToggle,
  DropdownMenu,
  DropdownItem,
  Button,
} from "reactstrap";
import moment from "moment";

export const column = (
  showSelect = false,
  onDelete,
  onEdit,
  onSelect,
  handleShowContent,
  t
) => [
  {
    name: t("dataSources.column.datasourcesName"),
    sortable: false,
    selector: (row) => row.name,
    cell: (row) => (
      <div className="d-flex justify-content-left align-items-center">
        <div className="d-flex align-items-center gap-1 fw-bold">
          <span>{row.name}</span>
        </div>
      </div>
    ),
  },
  {
    name: t("dataSources.column.datasourcesContent"),
    sortable: false,
    selector: (row) => row?.content,
    cell: (row) => (
      <Button.Ripple
        outline
        color="primary"
        onClick={() => handleShowContent(row.content, row)}
      >
        <Eye size={14} />
        <span className="align-middle ms-25 text-nowrap">
          {t("dataSources.column.seeContent")}
        </span>
      </Button.Ripple>
    ),
  },
  {
    name: t("general.createdAt"),
    sortable: false,
    sortField: "created_at",
    selector: (row) => row?.created_at,
    cell: (row) => (
      <span className="text-capitalize">
        {moment(row?.created_at).format("DD/MM/YYYY")}
      </span>
    ),
  },
  {
    name: t("general.actions"),
    minWidth: "100px",
    cell: (row) => {
      if (showSelect) {
        return (
          <Button
            onClick={() => onSelect(row)}
            color="primary"
            className="text-nowrap"
          >
            {t("dataSources.column.selectDatasource")}
          </Button>
        );
      }

      return (
        <div className="column-action">
          <UncontrolledDropdown>
            <DropdownToggle tag="div" className="btn btn-sm">
              <MoreVertical size={14} className="cursor-pointer" />
            </DropdownToggle>
            <DropdownMenu>
              <DropdownItem
                tag="a"
                href="/"
                className="w-100"
                onClick={(e) => {
                  e.preventDefault();
                  onEdit(row);
                }}
              >
                <Edit2 size={14} className="me-50" />
                <span className="align-middle text-nowrap">
                  {t("dataSources.column.editDatasources")}
                </span>
              </DropdownItem>
              <DropdownItem
                tag="a"
                href="/"
                className="w-100"
                onClick={(e) => {
                  e.preventDefault();
                  onDelete(row.id);
                }}
              >
                <Trash2 size={14} className="me-50" />
                <span className="align-middle text-nowrap">
                  {t("dataSources.column.deleteDatasources")}
                </span>
              </DropdownItem>
            </DropdownMenu>
          </UncontrolledDropdown>
        </div>
      );
    },
  },
];
