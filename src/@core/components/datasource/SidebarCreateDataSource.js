import Sidebar from "@components/sidebar";

import CreateDataSource from "./CreateDataSource";

const SidebarCreateDataSource = ({
  open,
  toggleSidebar,
  onCreate,
  bot,
  isUpdate = false,
}) => {
  const handleSidebarClosed = () => {};

  const handleToggleSidebar = () => {
    toggleSidebar();
  };

  return (
    <Sidebar
      size="50"
      open={open}
      title="Crear fuente de datos"
      headerClassName="mb-1"
      contentClassName="pt-0"
      toggleSidebar={handleToggleSidebar}
      onClosed={handleSidebarClosed}
    >
      <CreateDataSource onContinue={onCreate} isUpdate={isUpdate} bot={bot} />
    </Sidebar>
  );
};

export default SidebarCreateDataSource;
