import BubbleMessage from "../bubble-chat";
import "./styles.scss";

function ChatVisualization({
  backgroundMessageColor,
  backgroundMessageTextColor,
  welcomeMessage,
  botName = "Biitbot",
}) {
  return (
    <div className="chat-visualization">
      <div className="title">{botName}</div>
      <BubbleMessage
        sender="biitbot"
        message={welcomeMessage}
        botName={botName}
      />
      <BubbleMessage
        sender="Cliente"
        message="<PERSON><PERSON>, quiero saber si tienen honda civic 2018"
        backgroundMessageColor={backgroundMessageColor}
        backgroundMessageTextColor={backgroundMessageTextColor}
      />
      <BubbleMessage
        sender="biitbot"
        message="Claro, tenemos varios modelos, ¿cuál es tu presupuesto?"
        botName={botName}
      />
    </div>
  );
}

export default ChatVisualization;
