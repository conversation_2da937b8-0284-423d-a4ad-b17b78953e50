import { useState } from "react";
import {
  <PERSON>ton,
  Card,
  CardBody,
  CardHeader,
  CardSubtitle,
  CardTitle,
  Col,
  Input,
  Label,
  Row,
} from "reactstrap";

function WhatsappConfig({ onContinue, data, isUpdate = false }) {
  const [whatsAppId, setWhatsAppId] = useState(data?.whatsAppId || "");
  const [whatsappToken, setWhatsAppToken] = useState(data?.whatsappToken || "");

  const handleOnSubmit = async (e) => {
    e.preventDefault();
    onContinue({ whatsAppId, whatsappToken });
  };
  return (
    <Card>
      <CardHeader className="d-flex flex-column gap-1 flex-start">
        <CardTitle tag="h4" className="w-100">
          Configuración de WhatsApp
        </CardTitle>
        <CardSubtitle className="w-100">
          Agrega tu whatsapp Business ID y tu token de WhatsApp Business.
        </CardSubtitle>
      </CardHeader>
      <CardBody>
        <form onSubmit={handleOnSubmit}>
          <Row>
            <Col className="mb-2" md={12}>
              <Label>WhatsApp Business ID</Label>
              <Input
                name="whatsAppId"
                className="input-group-merge"
                value={whatsAppId}
                onChange={(e) => setWhatsAppId(e.target.value)}
              />
            </Col>
            <Col className="mb-2" md={12}>
              <Label>WhatsApp Business token</Label>
              <Input
                name="whatsappToken"
                className="input-group-merge"
                value={whatsappToken}
                onChange={(e) => setWhatsAppToken(e.target.value)}
              />
            </Col>
            {!isUpdate && (
              <Col md={12}>
                <Button.Ripple
                  type="submit"
                  color="primary"
                  className="mt-1"
                  block
                >
                  Saltar
                </Button.Ripple>
              </Col>
            )}
            <Col md={12}>
              <Button.Ripple
                {...(!isUpdate ? { outline: true } : {})}
                type="submit"
                color="primary"
                className="mt-1"
                block
              >
                {isUpdate ? "Actualizar" : "Guardar"}
              </Button.Ripple>
            </Col>
          </Row>
        </form>
      </CardBody>
    </Card>
  );
}

export default WhatsappConfig;
