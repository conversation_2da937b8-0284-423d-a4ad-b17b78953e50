import React, { Fragment } from "react";

import "./style.scss";

const BiitbotMessage = ({ botName, message }) => {
  return (
    <div className="bubble-container start mb-2">
      <div className="body-message">
        <div className="sender">{botName}</div>
        <div
          dangerouslySetInnerHTML={{ __html: message?.replace(/\n/g, "<br>") }}
          className="message bg-server radius-server"
          style={{ backgroundColor: "#f1f1f1", color: "#000" }}
        />
      </div>
    </div>
  );
};

const UserMessage = ({
  message,
  backgroundMessageColor,
  backgroundMessageTextColor,
}) => {
  return (
    <div className="bubble-container end mb-2 ms-5">
      <div className="body-message">
        <div className="sender">Client</div>
        <div
          className="message bg-user radius-user"
          style={{
            backgroundColor: backgroundMessageColor,
            color: backgroundMessageTextColor,
          }}
        >
          {message}
        </div>
      </div>
    </div>
  );
};

function BubbleMessage({
  sender,
  message,
  backgroundMessageColor,
  backgroundMessageTextColor,
  botName,
}) {
  return (
    <Fragment>
      {sender === "biitbot" ? (
        <BiitbotMessage message={message} botName={botName} />
      ) : (
        <UserMessage
          message={message}
          backgroundMessageColor={backgroundMessageColor}
          backgroundMessageTextColor={backgroundMessageTextColor}
        />
      )}
    </Fragment>
  );
}

export default BubbleMessage;
