.bubble-container {
  display: flex;

  .icon-message-container {
    display: flex;
    height: auto;
    align-items: flex-end;
    .icon-message {
      display: flex;
      align-items: flex-end;
      color: #ffff;
      width: 24px;
      height: 24px;
      background: #500d82;
      border-radius: 4px;
      justify-content: center;
    }
  }
  .mr {
    margin-right: 12px;
  }
  .ml {
    margin-left: 12px;
  }

  .body-message {
    .sender {
      font-size: 10px;
      margin-bottom: 0;
      padding-bottom: 0;
    }
    .message {
      width: 100%;

      font-style: normal;
      font-weight: 400;
      font-size: 14px;
      line-height: 24px;
      /* or 150% */

      letter-spacing: -0.005em;

      color: #000000;
      padding: 8px 8px 8px 8px;
    }
  }
}

.start {
  justify-content: start;
}

.end {
  justify-content: end;
}

.bg-server {
  background-color: #F2E3FD;
}

.bg-user {
  background-color: rgba(22, 51, 0, 0.14);
}

.radius-user {
  border-radius: 10px 10px 0px 10px;
}
.radius-server {
  border-radius: 10px 10px 10px 0px;
}
