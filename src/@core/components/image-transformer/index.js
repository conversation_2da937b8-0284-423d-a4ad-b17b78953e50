import { useCallback, useState } from "react";
import { Button, Input } from "reactstrap";
import <PERSON><PERSON><PERSON> from "react-easy-crop";

import getCroppedImg from "../../../utility/handle-image";

function ImageTransformer({
  title,
  description,
  defaultChatIcon,
  onComplete,
  onImageSelected,
}) {
  const [imageSelected, setImageSelected] = useState(null);
  const [croppedAreaPixels, setCroppedAreaPixels] = useState(null);
  const [croppedImage, setCroppedImage] = useState(null);
  const [zoom, setZoom] = useState(1);
  const [crop, setCrop] = useState({ x: 0, y: 0 });

  const showCroppedImage = useCallback(async () => {
    try {
      const croppedImage = await getCroppedImg(
        imageSelected,
        croppedAreaPixels,
        0
      );
      setCroppedImage(croppedImage);
      onComplete(croppedImage);
      setImageSelected(null);
    } catch (e) {}
  }, [imageSelected, croppedAreaPixels]);

  const onCropComplete = (...values) => {
    setCroppedAreaPixels(values[1]);
  };

  return (
    <div className="border rounded p-2">
      {title && <h4 className="mb-1">{title}</h4>}
      <div className="d-flex flex-column flex-md-row">
        {!imageSelected && (!!croppedImage || !!defaultChatIcon) && (
          <img
            className="rounded-circle me-2 mb-1 mb-md-0"
            src={croppedImage || defaultChatIcon}
            alt="featured img"
            width="46px"
            height="46px"
          />
        )}
        <div>
          {description && (
            <small className="text-muted">
              Recomienda una imagen de 96x96px o inferior, en formato JPG o PNG.
            </small>
          )}
          <div className="d-block">
            <div className="mb-0 mt-1">
              <Input
                type="file"
                id="exampleCustomFileBrowser"
                name="customFile"
                onChange={(e) => {
                  const file = e.target.files[0];
                  onImageSelected(file);
                  const reader = new FileReader();
                  reader.addEventListener("load", () => {
                    setImageSelected(reader.result);
                  });
                  reader.readAsDataURL(file);
                }}
                accept=".jpg, .png"
              />
            </div>
          </div>
        </div>
      </div>
      {imageSelected && (
        <div className="mt-1">
          <div
            className="position-relative"
            style={{ height: "500px", width: "100%" }}
          >
            <Cropper
              image={imageSelected}
              crop={crop}
              zoom={zoom}
              aspect={2 / 2}
              cropShape="round"
              cropSize={{ width: 96, height: 96 }}
              onCropChange={setCrop}
              onCropComplete={onCropComplete}
              onZoomChange={setZoom}
            />
          </div>
          <Button
            type="button"
            className="me-1 w-100 mt-1 mb-1 d-flex justify-content-center align-items-center"
            color="primary"
            onClick={showCroppedImage}
          >
            Recortar imagen
          </Button>
        </div>
      )}
    </div>
  );
}

export default ImageTransformer;
