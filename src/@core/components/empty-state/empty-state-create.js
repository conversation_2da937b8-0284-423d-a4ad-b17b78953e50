import React from "react";
import PropTypes from "prop-types";
import EmptyStateImage from "../../../../src/assets/empty-data.jpg";

const EmptyStateCreate = ({ message, title, children }) => {
  const emptyStateStyle = {
    display: "flex",
    flexDirection: "column",
    alignItems: "center",
    justifyContent: "center",
    background: "#fff"
  };

  const imageStyle = {
    maxWidth: "291px",
  };

  const paragraphStyle = {
    textAlign: "center",
    maxWidth: "282px",
    marginBottom: "12px",
  };

  const titleStyle = {
    textAlign: "center",
    maxWidth: "282px",
  };

  const contentStyle = {
    marginBottom: "52px",
  }

  return (
    <div style={emptyStateStyle}>
      <img src={EmptyStateImage} alt="Imagen Vacía" style={imageStyle} />
      <h3 style={titleStyle}>{title}</h3>
      <p style={paragraphStyle}>{message}</p>
      <div style={contentStyle}>
        {children}
      </div>

    </div>
  );
};

EmptyStateCreate.propTypes = {
  message: PropTypes.string.isRequired,
};

export default EmptyStateCreate;
