import React from "react";
import PropTypes from "prop-types";
import EmptyStateImage from "../../../../src/assets/empty-data.jpg";

const EmptyState = ({ message, title }) => {
  const emptyStateStyle = {
    display: "flex",
    flexDirection: "column",
    alignItems: "center",
    justifyContent: "center",
  };

  const imageStyle = {
    maxWidth: "291px",
  };

  const paragraphStyle = {
    textAlign: "center",
    maxWidth: "282px",
    marginBottom: "52px",
  };

  const titleStyle = {
    textAlign: "center",
    maxWidth: "282px",
  };

  return (
    <div style={emptyStateStyle}>
      <img src={EmptyStateImage} alt="Imagen Vacía" style={imageStyle} />
      <h3 style={titleStyle}>{title}</h3>
      <p style={paragraphStyle}>{message}</p>
    </div>
  );
};

EmptyState.propTypes = {
  message: PropTypes.string.isRequired,
};

export default EmptyState;
