// ** React Imports
import { Navigate } from "react-router-dom";
import { Suspense } from "react";

import Spinner from "../spinner/Loading-spinner";
import { getUserData } from "../../../auth/utils";

const PrivateRoute = ({ children, route }) => {
  const user = getUserData();

  if (route) {
    if (!user) {
      return <Navigate to="/login" />;
    }
  }

  return (
    <Suspense fallback={<Spinner className="content-loader" />}>
      {children}
    </Suspense>
  );
};

export default PrivateRoute;
