import { Menu, Settings } from "react-feather";
import { Container } from "reactstrap";

import whatsappIcon from "../../../assets/images/icons/whatsapp.png";

function WhatsappSuccess({ data }) {
  return (
    <Container fluid>
      <div className="gap-1 d-flex">
        <div className="block-container w-100">
          <h2>Para borrar whatsapp de Biitbot</h2>
          <ul>
            <li>Open WhatsApp on your phone.</li>
            <li>
              Go to settings by tapping on your profile photo, <b>Menu</b>{" "}
              <Menu size={16} />, or Settings <Settings size={16} />.
            </li>
            <li>
              Tap <b>linked devices</b>, select the device you want to unlink,
              and then <b> tap remove device</b>.
            </li>
          </ul>
        </div>
        <div className="w-100 d-flex flex-column justify-content-center align-items-center">
          <div className="d-flex flex-column gap-2">
            <div className="d-flex gap-1 align-items-center justify-content-center">
              <img width={40} src={whatsappIcon} />
              <h4 className="m-0 text-uppercase">
                Whatsapp conectado correctamente
              </h4>
            </div>
            <div className="d-flex flex-column gap-1">
              <p className="m-0">
                <b>Nombre:</b> {data?.phoneName}
              </p>
              <p className="m-0">
                <b>Numero de teléfono:</b> {data?.phoneNumber}
              </p>
            </div>
          </div>
        </div>
      </div>
    </Container>
  );
}

export default WhatsappSuccess;
