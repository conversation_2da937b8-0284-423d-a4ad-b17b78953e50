import { useEffect, useState } from "react";

import WhatsappConnection from "../../../views/extensions/whatsappConnection/whatsappConnection";
import { whatsappStates } from "../../../utility/whatsappEvents";
import WhatsappSuccess from "../whatsapp-success";
import useWhatsappConnection from "../../../hook/useWhatsappConnection";
import { ENV_SETTING } from "../../../utility/constants";

function WhatsappQr({ config, botRequestId }) {
  const API_URL = ENV_SETTING.VITE_WEBSOCKET_URL;
  console.log("websocket whatsapp qr url", API_URL);

  const [qrString, setQrString] = useState();
  const [linkedStatus, setLinkedStatus] = useState();
  const whatsappConnection = useWhatsappConnection(API_URL, botRequestId);

  useEffect(() => {
    setQrString(whatsappConnection.qrString);
    setLinkedStatus(whatsappConnection.linkedStatus);
  }, [whatsappConnection.linkedStatus, whatsappConnection.qrString]);

  if (linkedStatus === whatsappStates.LINKED) {
    return <WhatsappSuccess data={config} />;
  }

  return (
    <WhatsappConnection
      qrString={qrString}
      status={linkedStatus}
      onRequestQr={whatsappConnection.action.requestNewQr}
    />
  );
}

export default WhatsappQr;
