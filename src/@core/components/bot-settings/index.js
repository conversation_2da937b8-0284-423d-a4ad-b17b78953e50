import toast from "react-hot-toast";
import GoogleAuth from "../integratoins/google";
import OutlookAuth from "../integratoins/outlook";
import { deleteIntegration } from "../../../services/socialAuth";
import OrderAuth from "../integratoins/order";
import { Col } from "reactstrap";

function BotSettings({ bot }) {
  const hasGoogleCalendar = bot.selectedSocialAuth.find(
    (socialAuth) =>
      socialAuth.provider === "google" && socialAuth.type === "calendar"
  );

  const handleOnDelete = async (integrationId) => {
    try {
      await deleteIntegration(integrationId);
      toast.success("Integración eliminada");
    } catch (error) {
      toast.error("Error eliminando la integración");
    }
  };

  return (
    <>
      <h4>Integraciones</h4>
      <Col sm="6" className="mb-2">
        <GoogleAuth
          isLoggedIn={hasGoogleCalendar}
          botRequestId={bot?.botRequestId}
          onDelete={() => {
            handleOnDelete(hasGoogleCalendar?.id);
          }}
          initialData={hasGoogleCalendar.calendar}
        />
      </Col>
      <Col sm="6">
        <OrderAuth
          isActive={bot?.orderEnabled}
          botRequestId={bot?.botRequestId}
        />
      </Col>
      <div className="divider my-2">
        <div className="divider-text">
          Integraciones aun no disponibles pero pronto serán incorporadas
        </div>
      </div>
      <Col sm="6" className="opacity-50">
        <OutlookAuth isLoggedIn={false} />
      </Col>
    </>
  );
}

export default BotSettings;
