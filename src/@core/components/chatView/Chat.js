import { useEffect, useRef, useState } from "react";
import classnames from "classnames";
import PerfectScrollbar from "react-perfect-scrollbar";
import ReactMarkdown from "react-markdown";
import { Check, Send, X } from "react-feather";
import {
  Form,
  Input,
  Button,
  InputGroup,
  Label,
  Modal,
  ModalBody,
} from "reactstrap";
import Select from "react-select";
import ReactDOM from "react-dom";

import useChatSocket from "../../../hook/useChatSocket";
import { useTranslation } from "react-i18next";
import SidebarNewContact from "../../../views/contact/sidebarNewContact";
import { createContactFromChatSession } from "../../../services/contact";
import toast from "react-hot-toast";
import { getCustomerName } from "./utils";

const SYSTEM_ROLES = ["assistant", "system", "agent"];
const USER_ROLES = ["user"];

const Chat = ({
  chatSelected,
  handleOpenSideForMobile,
  toggleAiAssignment,
  onChatAssignment,
  teams,
}) => {
  const { t } = useTranslation();

  if (!chatSelected) {
    return null;
  }
  const chatArea = useRef(null);

  const assignedOptions =
    teams.length > 0
      ? teams.map((team) => ({
          value: team.id,
          label: team.name,
        }))
      : [];

  const [isAssignedToAi, setIsAssignedToAi] = useState(false);
  const [message, setMessage] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [imageModalIsOpen, setImageModalIsOpen] = useState(false);
  const [mediaToShow, setMediaToShow] = useState();
  const [isAddContactModalOpen, setIsAddContactModalOpen] = useState(false);
  const [showSaveContactButton, setSaveContactButton] = useState(true);

  const chatSocket = useChatSocket(
    chatSelected?.botCompany.botRequestId,
    chatSelected?.clientId
  );

  const handleAiStatusConditions = () => {
    if (
      chatSelected?.botCompany.isBotEnabled &&
      chatSelected?.shouldAiResponse
    ) {
      setIsAssignedToAi(true);
    } else {
      setIsAssignedToAi(false);
    }
  };

  useEffect(() => {
    handleAiStatusConditions();
  }, [chatSelected]);

  const renderChats = () => {
    return chatSocket?.chatHistory?.map((chat, index) => {
      if (!chat.content) return;

      return (
        <div key={`${chat?.id}-${index}`}>
          {SYSTEM_ROLES.includes(chat?.role) && (
            <div
              className={classnames("chat", {
                "chat-left": true,
              })}
            >
              <div className="chat-body">
                <div key={chat.content} className="chat-content">
                  <ReactMarkdown>{chat.content}</ReactMarkdown>
                </div>
              </div>
            </div>
          )}
          {USER_ROLES.includes(chat?.role) && (
            <div
              className={classnames("chat", {
                "chat-left": false,
              })}
            >
              <div className="chat-body">
                <div key={chat.content} className="chat-content">
                  {chat.type === "image" ? (
                    <img
                      width="150px"
                      style={{ cursor: "pointer" }}
                      onClick={() => {
                        setMediaToShow(chat.content);
                        setImageModalIsOpen(true);
                      }}
                      src={chat.content}
                      alt="chat image"
                    />
                  ) : (
                    <ReactMarkdown>{chat.content}</ReactMarkdown>
                  )}
                </div>
              </div>
            </div>
          )}
        </div>
      );
    });
  };

  const handleNewMessageSubmit = (e) => {
    e.preventDefault();
    chatSocket.sendNewMessage(message);
    setMessage("");
  };

  const scrollToBottom = () => {
    const chatContainer = ReactDOM.findDOMNode(chatArea.current);
    chatContainer.scrollTop = Number.MAX_SAFE_INTEGER;
  };

  const ChatWrapper =
    Object.keys(chatSocket?.chatHistory).length > 0 ? PerfectScrollbar : "div";

  const handleCreateNewContact = async (contact) => {
    contact.shouldAiResponse = false;
    Object.assign(contact, { chatSession: chatSelected.clientId });

    const response = await createContactFromChatSession(contact).catch(
      (err) => err
    );

    if (response?.response?.data?.message === "contact_exist") {
      toast.success(t("contact.contactUpdated"));
      setIsAddContactModalOpen(false);
      setSaveContactButton(false);
      return true;
    } else if (
      response?.code === "ERR_BAD_REQUEST" &&
      response?.response?.data?.message !== "contact_exist"
    ) {
      toast.error("contact.contactNotCreated");
      return false;
    }

    toast.success(t("contact.contactCreated"));
    setIsAddContactModalOpen(false);
    setSaveContactButton(false);
    return true;
  };

  useEffect(() => {
    const selectedUserLen = Object.keys(chatSocket?.chatHistory).length;

    if (selectedUserLen) {
      scrollToBottom();
    }
  }, [chatSocket?.chatHistory]);

  return (
    <div className="chat-app-window">
      <div
        className={classnames("start-chat-area", {
          "d-none": Object.keys(chatSelected || {}).length > 0,
        })}
      ></div>
      {chatSelected ? (
        <div
          className={classnames("active-chat d-flex flex-column", {
            "d-none": Object.keys(chatSelected).length === 0,
          })}
        >
          <div className="chat-navbar">
            <header className="chat-header h-auto">
              <div className="d-flex p-1 align-items-center justify-content-between w-100">
                <div className="d-flex align-items-center gap-2 flex-row gap-1 justify-content-center">
                  <div className="">
                    <Label className="form-label" for="fullName">
                      {t("hub.chat.chatAssignedTo")}
                    </Label>
                    <Select
                      id="assigned"
                      name="assigned"
                      options={assignedOptions}
                      value={{
                        value: chatSelected?.team?.id,
                        label: chatSelected?.team?.name,
                      }}
                      onChange={(e) => onChatAssignment(e)}
                      placeholder={t('hub.chat.assignChat')}
                    />
                  </div>
                  {showSaveContactButton && !chatSelected?.contact && (
                    <div className="mt-2">
                      <Button
                        color="primary"
                        outline
                        onClick={() => setIsAddContactModalOpen(true)}
                      >
                        {t("contact.saveContact")}
                      </Button>
                    </div>
                  )}
                </div>
                <div className="d-flex gap-1 align-items-center">
                  <h6 className="mb-0">
                    {isAssignedToAi ? t('hub.chat.aiEnabled') : t('hub.chat.aiDisabled')}
                  </h6>
                  <div className="form-switch cursor-pointer">
                    <Input
                      type="switch"
                      id="toggle-ai"
                      disabled={isLoading}
                      checked={isAssignedToAi}
                      onChange={async () => {
                        setIsLoading(true);
                        setIsAssignedToAi(!isAssignedToAi);
                        await toggleAiAssignment(chatSelected?.clientId);
                        setIsLoading(false);
                      }}
                    />
                    <Label className="form-check-label" for="toggle-ai">
                      <span className="switch-icon-left">
                        <Check size={14} />
                      </span>
                      <span className="switch-icon-right">
                        <X size={14} />
                      </span>
                    </Label>
                  </div>
                </div>
              </div>
            </header>
          </div>

          <ChatWrapper
            ref={chatArea}
            className="user-chats"
            options={{ wheelPropagation: false }}
          >
            <div className="chats">{renderChats()}</div>
          </ChatWrapper>

          <Form className="chat-app-form" onSubmit={handleNewMessageSubmit}>
            <InputGroup className="input-group-merge me-1 form-send-message">
              <Input
                disabled={isAssignedToAi}
                value={message}
                onChange={(e) => setMessage(e.target.value)}
                placeholder={t('hub.chat.typeMessage')}
              />
            </InputGroup>
            <Button
              disabled={isAssignedToAi}
              className="send"
              color="primary"
              type="submit"
            >
              <Send size={14} className="d-lg-none" />
              <span className="d-none d-lg-block">{t("hub.chat.send")}</span>
            </Button>
          </Form>
        </div>
      ) : null}
      <Modal
        isOpen={imageModalIsOpen}
        onClosed={() => {
          setImageModalIsOpen(false);
          setMediaToShow(null);
        }}
        toggle={() => setImageModalIsOpen(!imageModalIsOpen)}
        centered
        className="modal-dialog-centered modal-lg"
      >
        <ModalBody>
          <div className="w-100">
            <img
              style={{ width: "-webkit-fill-available" }}
              src={mediaToShow}
              alt="media"
            />
          </div>
        </ModalBody>
      </Modal>
      <SidebarNewContact
        open={isAddContactModalOpen}
        toggleSidebar={() => setIsAddContactModalOpen(!isAddContactModalOpen)}
        onCreate={handleCreateNewContact}
        defaultValue={{
          name: getCustomerName(
            chatSelected,
            chatSelected?.botCompany?.chatProvider
          ),
          phone: chatSelected?.whatsappChat?.fromPhoneNumber,
        }}
      />
    </div>
  );
};

export default Chat;
