// ** Custom Components
import Avatar from '@components/avatar'

// ** Third Party Components
import classnames from 'classnames'
import PerfectScrollbar from 'react-perfect-scrollbar'
import { useTranslation } from 'react-i18n'

// ** Reactstrap Imports
import { X, Mail, PhoneCall, Clock, Tag, Star, Image, Trash, Slash } from 'react-feather'

const UserProfileSidebar = props => {
  // ** Hooks
  const { t } = useTranslation()
  
  // ** Props
  const { user, handleUserSidebarRight, userSidebarRight } = props

  return (
    <div className={classnames('user-profile-sidebar', { show: userSidebarRight === true })}>
      <header className='user-profile-header'>
        <span className='close-icon' onClick={handleUserSidebarRight}>
          <X size={14} />
        </span>
        <div className='header-profile-sidebar'>
          <Avatar
            className='box-shadow-1 avatar-border'
            size='xl'
            status={user.status}
            img={user.avatar}
            imgHeight='70'
            imgWidth='70'
          />
          <h4 className='chat-user-name'>{user.fullName}</h4>
          <span className='user-post'>{user.role}</span>
        </div>
      </header>
      <PerfectScrollbar className='user-profile-sidebar-area' options={{ wheelPropagation: false }}>
        <h6 className='section-label mb-1'>{t('userProfile.about')}</h6>
        <p>{user.about || t('userProfile.noDescription')}</p>
        <div className='personal-info'>
          <h6 className='section-label mb-1 mt-3'>{t('userProfile.personalInfo')}</h6>
          <ul className='list-unstyled'>
            <li className='mb-1'>
              <Mail className='me-75' size={17} />
              <span className='align-middle'>{user.email || t('userProfile.noEmail')}</span>
            </li>
            <li className='mb-1'>
              <PhoneCall className='me-50' size={17} />
              <span className='align-middle'>{user.phone || t('userProfile.noPhone')}</span>
            </li>
            <li>
              <Clock className='me-50' size={17} />
              <span className='align-middle'>{t('userProfile.availability')}</span>
            </li>
          </ul>
        </div>
        <div className='more-options'>
          <h6 className='section-label mb-1 mt-3'>{t('userProfile.options')}</h6>
          <ul className='list-unstyled'>
            <li className='cursor-pointer mb-1'>
              <Tag className='me-50' size={17} />
              <span className='align-middle'>{t('userProfile.addTag')}</span>
            </li>
            <li className='cursor-pointer mb-1'>
              <Star className='me-50' size={17} />
              <span className='align-middle'>{t('userProfile.importantContact')}</span>
            </li>
            <li className='cursor-pointer mb-1'>
              <Image className='me-50' size={17} />
              <span className='align-middle'>{t('userProfile.sharedMedia')}</span>
            </li>
            <li className='cursor-pointer mb-1'>
              <Trash className='me-50' size={17} />
              <span className='align-middle'>{t('userProfile.deleteContact')}</span>
            </li>
            <li className='cursor-pointer'>
              <Slash className='me-75' size={17} />
              <span className='align-middle'>{t('userProfile.blockContact')}</span>
            </li>
          </ul>
        </div>
      </PerfectScrollbar>
    </div>
  )
}

export default UserProfileSidebar
