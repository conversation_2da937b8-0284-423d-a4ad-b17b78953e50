import { useEffect, useState } from "react";
import PerfectScrollbar from "react-perfect-scrollbar";
import classNames from "classnames";
import moment from "moment";
import {
  Badge,
  DropdownItem,
  DropdownMenu,
  DropdownToggle,
  UncontrolledDropdown,
  InputGroup,
  InputGroupText,
  Input,
} from "reactstrap";
import { MoreVertical, Trash2, Search } from "react-feather";
import { toast } from "react-hot-toast";
import { useTranslation } from "react-i18next";

import whatsappIcon from "../../../assets/images/icons/whatsapp.png";
import webIcon from "../../../assets/images/icons/internet.png";
import instagramIcon from "../../../assets/images/icons/social/instagram.png";
import { deleteChatBySessionId } from "../../../services/chat";
import { getCustomerName } from "./utils";

const RenderChatIcon = ({ provider }) => {
  if (provider === "whatsapp") {
    return <img width="35px" src={whatsappIcon} />;
  } else if (provider === "web") {
    return <img width="35px" src={webIcon} />;
  } else if (provider === "instagram") {
    return <img width="25px" src={instagramIcon} />;
  }
};

const RenderChatDropDown = ({ sessionId, onDelete }) => {
  const { t } = useTranslation();

  const handleChatDelete = async () => {
    await deleteChatBySessionId(sessionId)
      .then(() => {
        toast.success(t("hub.chat.chatDeleted"));
      })
      .finally(() => onDelete())
      .catch(() => toast.error(t("hub.chat.chatDeleteError")));
  };

  return (
    <div className="column-action">
      <UncontrolledDropdown>
        <DropdownToggle tag="div" className="btn btn-sm">
          <MoreVertical size={14} className="cursor-pointer" />
        </DropdownToggle>
        <DropdownMenu>
          <DropdownItem
            tag="a"
            href="/"
            className="w-100"
            onClick={(e) => {
              e.preventDefault();
              handleChatDelete();
            }}
          >
            <Trash2 size={14} className="me-50" />
            <span className="align-middle">{t("general.delete")}</span>
          </DropdownItem>
        </DropdownMenu>
      </UncontrolledDropdown>
    </div>
  );
};

const SidebarLeft = ({
  data,
  handleChatSelected,
  openSideForMobile,
  onDelete,
}) => {
  const { t } = useTranslation();
  const [isSideBarOpenForMobile, setIsSideBarOpenForMobile] = useState(false);
  const [selectedChat, setSelectedChat] = useState();
  const [search, setSearch] = useState("");
  const [filteredData, setFilteredData] = useState([]);

  const chatHaveNotifications = (chat) => {
    const messagesUnread = chat?.chatResponseGenerator?.filter(
      (message) => message.viewed === false
    );

    if (selectedChat?.clientId === chat?.clientId) {
      return false;
    }

    return messagesUnread?.length > 0 ? (
      <Badge color="danger" pill>
        {chat.shouldAiResponse ? (
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
          >
            <path d="M12 8V4H8"></path>
            <rect width="16" height="12" x="4" y="8" rx="2"></rect>
            <path d="M2 14h2"></path>
            <path d="M20 14h2"></path>
            <path d="M15 13v2"></path>
            <path d="M9 13v2"></path>
          </svg>
        ) : (
          messagesUnread?.length
        )}
      </Badge>
    ) : (
      false
    );
  };

  useEffect(() => {
    setIsSideBarOpenForMobile(openSideForMobile);
  }, [openSideForMobile]);

  // TODO: refactor the search chat feature
  useEffect(() => {
    if (search) {
      setFilteredData(
        data.filter((chat) => {
          if (chat?.contact?.name) {
            return chat?.contact?.name.includes(search);
          }
          if (chat?.whatsappChat) {
            return chat?.whatsappChat?.fromPhoneNumber.includes(search);
          } else if (chat?.facebookChat?.recipientName) {
            return chat?.facebookChat?.recipientName.includes(search);
          } else {
            return chat?.clientId?.includes(search);
          }
        })
      );
    } else {
      setFilteredData(data);
    }
  }, [data, search]);

  const renderChats = (chat, index) => {
    return (
      <li
        className={classNames("d-flex border-top border-bottom", {
          active: selectedChat?.id === chat?.id,
        })}
        key={`${chat.id}-${index}`}
        onClick={() => {
          setSelectedChat(chat);
          handleChatSelected(chat.id);
        }}
      >
        <div className="d-flex flex-column gap-1">
          <RenderChatIcon provider={chat?.botCompany?.chatProvider} />
          <Badge color="primary mb-1">{chat?.botCompany?.botName}</Badge>
        </div>
        <div className="chat-info">
          <h5 className="mb-1">
            {getCustomerName(chat, chat?.botCompany?.chatProvider)}
          </h5>
          <small className="float-start chat-time text-uppercase">
            {moment(chat?.updatedAt).format("DD/MMM/yyyy")}
          </small>
        </div>
        <div className="d-flex align-items-center flex-column gap-1">
          <RenderChatDropDown sessionId={chat?.clientId} onDelete={onDelete} />
          {chatHaveNotifications(chat)}
        </div>
      </li>
    );
  };

  return (
    <div className="sidebar-left">
      <div className="sidebar">
        <div
          className={classNames("sidebar-content", {
            show: isSideBarOpenForMobile,
          })}
        >
          <div className="chat-fixed-search p-2">
            <div className="d-flex align-items-center w-100">
              <InputGroup className="input-group-merge ms-1 w-100">
                <InputGroupText className="round">
                  <Search className="text-muted" size={14} />
                </InputGroupText>
                <Input
                  value={search}
                  className="round"
                  placeholder={t("hub.chat.searchChatByName")}
                  onChange={(e) => {
                    setSearch(e.target.value);
                  }}
                />
              </InputGroup>
            </div>
          </div>
          <PerfectScrollbar
            className="chat-user-list-wrapper list-group"
            options={{ wheelPropagation: true }}
          >
            <h4 className="chat-list-title">{t("general.conversations")}</h4>
            {filteredData?.map(renderChats)}
          </PerfectScrollbar>
        </div>
      </div>
    </div>
  );
};

export default SidebarLeft;
