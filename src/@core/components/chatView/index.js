import { Fragment, useState, useEffect } from "react";
import classnames from "classnames";
import { toast } from "react-hot-toast";

import Chat from "./Chat";
import Sidebar from "./SidebarLeft";
import { assignChatToTeam, toggleAi } from "../../../services/chat";
import Inbox from "./Inbox";
import { getTeamsList } from "../../../services/teams";
import useInboxSocket from "../../../hook/useInbox";

import { useTranslation } from "react-i18next";

import "@styles/base/pages/app-chat.scss";
import "@styles/base/pages/app-chat-list.scss";

const ChatView = () => {
  const { t } = useTranslation();
  const inboxSocket = useInboxSocket();

  const [chats, setChats] = useState([]);
  const [sidebar, setSidebar] = useState(false);
  const [userSidebarRight, setUserSidebarRight] = useState(false);
  const [userSidebarLeft, setUserSidebarLeft] = useState(false);
  const [chatSelected, setChatSelected] = useState();
  const [teams, setTeams] = useState([]);

  const handleOverlayClick = () => {
    setSidebar(false);
    setUserSidebarRight(false);
    setUserSidebarLeft(false);
  };

  const handleChatSelected = (id) => {
    const chat = chats.find((bot) => bot.id === id);
    const messagesId = [];
    for (const message of chat.chatResponseGenerator) {
      if (!message.viewed) {
        messagesId.push(message.id);
      }
    }
    inboxSocket.actions.markChatAsRead(messagesId);
    setChatSelected(chat);
  };

  const handleOpenSideForMobile = () => {
    setSidebar(!sidebar);
  };

  const toggleAiAssignment = async (sessionId) => {
    await toggleAi(sessionId);
  };

  const handleChatAssignment = async (data) => {
    assignChatToTeam(chatSelected.id, data.value)
      .then((res) => {
        toast.success(t("hub.chatAssigned"));

        const newChatSelected = { ...chatSelected };
        newChatSelected.team = res.team;
        setChatSelected(newChatSelected);
      })
      .catch(() => {
        return toast.error(t("hub.error.couldNotAssignedToTheTeam"));
      });
  };

  const filterChats = (inbox) => {
    if (inbox === "GENERAL") {
      setChats(inboxSocket.inbox);
    } else {
      const unreadChat = [];
      for (const chat of inboxSocket.inbox) {
        const unreadMessages = chat.chatResponseGenerator.filter(
          (message) => !message.viewed
        );

        if (unreadMessages.length > 0) {
          unreadChat.push(chat);
        }
      }

      setChats(unreadChat);
    }
  };

  // Cargar los equipos solo una vez al montar el componente
  useEffect(() => {
    const loadTeams = async () => {
      try {
        const teamsData = await getTeamsList();
        setTeams(teamsData);
      } catch (error) {
        console.error('Error al cargar los equipos:', error);
      }
    };

    loadTeams();

    return () => {
      // Limpiar estados pero mantener la conexión del socket
      setChats([]);
      setChatSelected(null);
      setTeams([]);
    };
  }, []);
  
  // Manejar cambios en el estado del socket
  useEffect(() => {
    if (inboxSocket?.connectionStatus === 'disconnected') {
      console.log('Socket desconectado, intentando reconectar...');
      const reconnectTimer = setTimeout(() => {
        if (inboxSocket.actions.reconnect) {
          inboxSocket.actions.reconnect();
        }
      }, 2000);
      
      return () => clearTimeout(reconnectTimer);
    }
  }, [inboxSocket?.connectionStatus]);

  useEffect(() => {
    filterChats("GENERAL");
  }, [inboxSocket.inbox]);

  return (
    <Fragment>
      <Inbox
        chats={inboxSocket.inbox}
        onClick={(inbox) => filterChats(inbox)}
        connectionStatus={inboxSocket.connectionStatus}
        actions={inboxSocket.actions}
        onStatusChange={(status) =>
          inboxSocket.actions.changeConnectionStatus?.(status)
        }
      />
      <div className="d-flex h-100 flex-auto">
        <Sidebar
          data={inboxSocket.inbox}
          handleChatSelected={handleChatSelected}
          onDelete={() => {
            inboxSocket.actions.updateInbox();
            setChatSelected();
          }}
          openSideForMobile={sidebar}
        />
        <div className="content-right">
          <div className="content-wrapper">
            <div className="content-body">
              <div
                className={classnames("body-content-overlay", {
                  show:
                    userSidebarRight === true ||
                    sidebar === true ||
                    userSidebarLeft === true,
                })}
                onClick={handleOverlayClick}
              ></div>
              {chatSelected && (
                <Chat
                  chatSelected={chatSelected}
                  handleOpenSideForMobile={handleOpenSideForMobile}
                  toggleAiAssignment={toggleAiAssignment}
                  onChatAssignment={handleChatAssignment}
                  teams={teams}
                />
              )}
            </div>
          </div>
        </div>
      </div>
    </Fragment>
  );
};

export default ChatView;
