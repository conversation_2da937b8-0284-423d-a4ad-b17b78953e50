import { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { <PERSON><PERSON>, Spinner } from "reactstrap";
import { RefreshCw, Wifi, WifiOff } from "react-feather";

import "./inbox.scss";

function Inbox({
  chats = [],
  onClick = () => {},
  connectionStatus,
  actions = {},
  onStatusChange = () => {}
}) {
  const { t } = useTranslation();

  const [generalCount, setGeneralCount] = useState(0);
  const [, setIsHovering] = useState(false);
  
  // Eliminamos handleConnectionStatus ya que no se usa

  useEffect(() => {
    setGeneralCount(chats.length);
  }, [chats]);

  const getConnectionStatus = () => {
    if (connectionStatus === 'connecting') return t('hub.connection.connecting');
    return connectionStatus === 'connected' 
      ? t('hub.connection.connected') 
      : t('hub.connection.disconnected');
  };

  const getConnectionIcon = () => {
    if (connectionStatus === 'connecting') {
      return <Spinner size="sm" />;
    }
    return connectionStatus === 'connected' ? (
      <Wifi size={18} />
    ) : (
      <WifiOff size={18} />
    );
  };

  // Función para manejar la reconexión
  const handleReconnect = () => {
    if (actions?.reconnect) {
      actions.reconnect();
    } else if (onStatusChange) {
      // Soporte para la implementación anterior
      onStatusChange('connected');
    }
  };

  return (
    <div className="inbox d-flex flex-column h-100 p-2">
      <div className="connection-status d-flex justify-content-between align-items-center mb-2">
        <div 
          className="d-flex align-items-center gap-1"
          onMouseEnter={() => setIsHovering(true)}
          onMouseLeave={() => setIsHovering(false)}
        >
          <div className={`connection-dot ${connectionStatus}`}>
            {getConnectionIcon()}
          </div>
          <span className="status-text">{getConnectionStatus()}</span>
        </div>
        
        {connectionStatus !== 'connected' && (
          <Button 
            color="primary" 
            size="sm" 
            onClick={handleReconnect}
            disabled={connectionStatus === 'connecting'}
            className="reconnect-btn"
          >
            <RefreshCw size={14} className={connectionStatus === 'connecting' ? 'spinning' : ''} />
            {connectionStatus === 'connecting' 
              ? t('hub.connection.connecting')
              : t('hub.connection.reconnect')}
          </Button>
        )}
      </div>
      <div className="divider" />
      <h6>{t("hub.inbox.title")}</h6>
      <ul className="list-group list-group-flush">
        <li
          className="list-group-item d-flex justify-content-between align-items-start gap-1 pb-2"
          onClick={() => onClick("GENERAL")}
        >
          <div className="">
            <div className="fw-bold">{t("hub.inbox.inbox")}</div>
          </div>
          <span className="badge bg-primary rounded-pill">{generalCount}</span>
        </li>
        <div className="divider" />
      </ul>
    </div>
  );
}

export default Inbox;
