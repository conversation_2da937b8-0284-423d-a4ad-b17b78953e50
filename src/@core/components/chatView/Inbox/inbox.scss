.inbox {
  background-color: #fff;
  border-right: 1px solid #dee2e6;
  width: 330px;
  flex: none;
  padding: 1rem;

  .connection-status {
    padding: 0.5rem;
    border-radius: 6px;
    background-color: #f8f9fa;
    margin-bottom: 1rem;

    .connection-dot {
      width: 20px;
      height: 20px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.3s ease;

      &.connected {
        color: #28a745;
      }

      &.disconnected {
        color: #dc3545;
      }

      &.connecting {
        color: #ffc107;
      }
    }

    .status-text {
      font-size: 0.9rem;
      font-weight: 500;
      margin-left: 0.5rem;
    }

    .reconnect-btn {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      font-size: 0.8rem;
      padding: 0.25rem 0.75rem;
      
      .spinning {
        animation: spin 1s linear infinite;
      }
    }
  }

  ul li {
    cursor: pointer;
    transition: background-color 0.2s ease;
    
    &:hover {
      background-color: #f8f9fa;
    }
  }

  @keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
  }
}