import React, { useEffect, useState, useMemo } from "react";
import { <PERSON><PERSON>, <PERSON>, CardBody, CardHeader } from "reactstrap";
import { useTranslation } from "react-i18next";
import Select from "react-select";
import toast from "react-hot-toast";
import { Instagram, Plus, X } from "react-feather";

import {
  updateSelectedSocialAuth,
  getIntegration,
  unlinkIntegration,
} from "../../../../services/socialAuth";

const FacebookAuth = ({ botRequestId }) => {
  const [socialAuths, setSocialAuths] = useState([]);
  const [selectedAccountOption, setSelectedAccountOption] = useState(null);
  const [accountOptions, setAccountOptions] = useState([]);
  const [linkedAccount, setLinkedAccount] = useState([]);
  const { t } = useTranslation();

  const filteredChatAccountOptions = useMemo(() => {
    if (!accountOptions) return [];
    return accountOptions.filter((option) => option && option.type === "chat");
  }, [accountOptions]);

  const loadIntegration = async () => {
    try {
      const integrations = await getIntegration();
      setSocialAuths(integrations || []);

      if (integrations && integrations?.socialAuthAccount?.length > 0) {
        const options = integrations.socialAuthAccount.map((auth) => ({
          label: `${auth.accountName} (${auth.accountId})`,
          value: auth.id,
          account: auth,
        }));

        setAccountOptions(options);

        const currentSelectedAuth = integrations.socialAuthAccount.filter(
          (auth) =>
            auth.selectedBotCompany &&
            auth.selectedBotCompany.botRequestId === botRequestId
        );

        if (currentSelectedAuth.length > 0) {
          setLinkedAccount(currentSelectedAuth);
        }
      } else {
        setSocialAuths([]);
        setAccountOptions([]);
        setSelectedAccountOption(null);
      }
    } catch (error) {
      toast.error(t("facebookIntegration.errorLoadingIntegration"));
      setSocialAuths([]);
      setAccountOptions([]);
      setSelectedAccountOption(null);
    }
  };

  const handleAccountSelectionChange = async (
    selectedOption,
    isInitialSelection = false
  ) => {
    if (!selectedOption || !selectedOption.value) return;

    try {
      if (
        !isInitialSelection ||
        (selectedOption.account &&
          selectedOption.account.selectedBotCompany &&
          selectedOption.account.selectedBotCompany.botRequestId ===
            botRequestId)
      ) {
        await updateSelectedSocialAuth({
          socialAuthId: selectedOption.value,
          botRequestId,
        });
        toast.success(t("facebookIntegration.activeAccountUpdated"));
      }
      setSelectedAccountOption(selectedOption);
      setLinkedAccount((prev) => {
        const existingAccount = prev.find(
          (account) => account.id === selectedOption.value
        );
        if (existingAccount) {
          return prev;
        }
        return [...prev, selectedOption.account];
      });
    } catch (error) {
      toast.error(t("facebookIntegration.errorUpdatingActiveAccount"));
    }
  };

  const handleUnlinkIntegration = async (integrationId) => {
    try {
      await unlinkIntegration(integrationId);
      toast.success(t("facebookIntegration.integrationUnlinked"));
      setLinkedAccount((prev) =>
        prev.filter((account) => account.id !== integrationId)
      );
    } catch (error) {
      toast.error(t("facebookIntegration.errorUnlinkingIntegration"));
    }
  };

  useEffect(() => {
    loadIntegration();
  }, [botRequestId]);

  const handleFacebookLogin = () => {
    FB.login(
      function (response) {
        console.log(response);
      },
      {
        config_id: "****************",
        response_type: "code",
        override_default_response_type: true,
      }
    );
  };

  return (
    <Card>
      <CardHeader>
        <h4>{t("facebookIntegration.instagramIntegration")}</h4>
      </CardHeader>
      <CardBody className="text-center">
        <p>{t("facebookIntegration.connectInstagramAccount")}</p>
        {filteredChatAccountOptions.length > 0 ? (
          <div className="d-flex flex-column align-items-center mb-3">
            <ul className="list-group mb-3" style={{ maxWidth: "400px" }}>
              {linkedAccount
                .filter((option) => option.type === "chat")
                .map((option) => (
                  <li
                    key={option.value}
                    className="list-group-item d-flex justify-content-between align-items-center"
                  >
                    {option.accountName} ({option.accountId})
                    <button
                      className="btn"
                      onClick={() => handleUnlinkIntegration(option.id)}
                    >
                      <X size={18} />
                    </button>
                  </li>
                ))}
            </ul>
            <div
              className="d-flex gap-2 align-items-center justify-content-center w-100 mb-3"
              style={{ maxWidth: "400px" }}
            >
              <Select
                className="react-select-container flex-grow-1"
                classNamePrefix="react-select"
                options={filteredChatAccountOptions}
                value={selectedAccountOption}
                onChange={(selectedOption) =>
                  handleAccountSelectionChange(selectedOption, false)
                }
                isClearable={false}
              />
            </div>
            <Button color="info" onClick={handleFacebookLogin}>
              <Plus size={22} />{" "}
              {t("facebookIntegration.linkAnotherInstagramAccount")}
            </Button>
          </div>
        ) : (
          <Button color="primary" onClick={handleFacebookLogin}>
            <Instagram size={22} />{" "}
            {t("facebookIntegration.connectWithInstagram")}
          </Button>
        )}
        {socialAuths.length === 0 && accountOptions.length === 0 && (
          <p className="mt-2">
            {t("facebookIntegration.noActiveIntegrationFound")}
          </p>
        )}
      </CardBody>
    </Card>
  );
};

export default FacebookAuth;
