import googleSvg from "./../../../../assets/images/icons/google-calendar.png";
import { Button, Input, Label } from "reactstrap";
import { Check, Edit, X } from "react-feather";
import { useEffect, useState } from "react";
import { getGoogleAuthUrl } from "../../../../services/socialAuth";
import toast from "react-hot-toast";
import { useGoogleLogout } from "react-google-login";
import SidebarCompleteCalendarIntegration from "../../../../views/pages/bots/SidebarCompleteCalendarIntegration";
import { updateCalendarSettings } from "../../../../services/calendar";

function GoogleAuth({
  isLoggedIn = false,
  botRequestId,
  onDelete,
  initialData,
}) {
  const [checked, setChecked] = useState(false);
  const [sidebarOpen, setSidebarOpen] = useState(false);

  const handleGoogleLogin = async () => {
    const authUrl = await getGoogleAuthUrl(botRequestId);
    window.location.href = authUrl;
  };

  const handleSwitchChange = async (event) => {
    if (event.target.checked) {
      try {
        handleGoogleLogin();
        setChecked(true);
      } catch (error) {
        useGoogleLogout();
        setChecked(false);
        toast.error("Error by doing the integration");
      }
    } else {
      setChecked(false);
      onDelete();
    }
  };

  const handleToggleSidebar = () => {
    setSidebarOpen(!sidebarOpen);
  };

  const handleCalendarUpdate = (data) => {
    const response = updateCalendarSettings(data.calendarId, data);
    if (response) {
      toast.success("Integración actualizada");
    } else {
      toast.error("Error actualizando la integración");
    }
  };

  useEffect(() => {
    setChecked(isLoggedIn);
  }, [isLoggedIn]);

  return (
    <>
      <div className="d-flex mt-2">
        <div className="flex-shrink-0">
          <img className="me-1" src={googleSvg} alt="google" width="38" />
        </div>
        <div className="d-flex align-item-center justify-content-between flex-grow-1">
          <div className="me-1">
            <p className="fw-bolder mb-0">Google calendar</p>
            <span>Permitir que el bot acceda al calendario de google.</span>
          </div>
          <div className="mt-50 mt-sm-0 d-flex align-items-center gap-1">
            <div className="form-switch">
              <>
                <Input
                  type="switch"
                  checked={checked}
                  id={`account-google-calendar`}
                  onChange={handleSwitchChange}
                />
                <Label
                  className="form-check-label"
                  for={`account-google-calendar`}
                >
                  <span className="switch-icon-left">
                    <Check size={14} />
                  </span>
                  <span className="switch-icon-right">
                    <X size={14} />
                  </span>
                </Label>
              </>
            </div>
            {checked && <Button onClick={handleToggleSidebar} color="link" size="sm">
              <Edit size={18} /> Editar
            </Button>}
          </div>
        </div>
      </div>

      <SidebarCompleteCalendarIntegration
        open={sidebarOpen}
        toggleSidebar={handleToggleSidebar}
        onSubmit={(data) => handleCalendarUpdate(data)}
        botRequestId={botRequestId}
        isLoading={false}
        initialData={initialData}
      />
    </>
  );
}

export default GoogleAuth;
