import { useState } from "react";
import { Input, Label } from "reactstrap";
import { Check, X } from "react-feather";
import toast from "react-hot-toast";

import shopOnline from "./../../../../assets/images/icons/shop-online.png";
import { toggleOrderIntegration } from "../../../../services/bot";

function OrderAuth({ isActive, botRequestId }) {
  const [checked, setChecked] = useState(isActive || false);

  const handleSwitchChange = async (event) => {
    if (event.target.checked) {
      handleToggleOrder(true);
      setChecked(true);
    } else {
      handleToggleOrder(false);
      setChecked(false);
    }
  };

  const handleToggleOrder = async (enabled) => {
    const response = await toggleOrderIntegration(botRequestId, enabled);

    if (response) {
      toast.success("Integración habilitada");
    } else {
      toast.error("Error habilitando la integración");
    }
  };

  return (
    <>
      <div className="d-flex mt-2">
        <div className="flex-shrink-0">
          <img className="me-1" src={shopOnline} alt="shop online" width="38" />
        </div>
        <div className="d-flex align-item-center justify-content-between flex-grow-1">
          <div className="me-1">
            <p className="fw-bolder mb-0">Biitbot Order</p>
            <span>
              Permitir que Biitbot pueda recibir y procesar ordenes de compra.
            </span>
          </div>
          <div className="mt-50 mt-sm-0">
            <div className="form-switch">
              <>
                <Input
                  type="switch"
                  checked={checked}
                  id={`order-integration`}
                  onChange={handleSwitchChange}
                />
                <Label className="form-check-label" for={`order-integration`}>
                  <span className="switch-icon-left">
                    <Check size={14} />
                  </span>
                  <span className="switch-icon-right">
                    <X size={14} />
                  </span>
                </Label>
              </>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}

export default OrderAuth;
