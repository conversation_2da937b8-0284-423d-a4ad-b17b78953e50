import { GoogleLogin, googleLogout, useGoogleLogin } from "@react-oauth/google";
import googleSvg from "./../../../../assets/images/icons/Outlook-Logo.png";
import { Input, Label } from "reactstrap";
import { Check, X } from "react-feather";

function OutlookAuth({ isLoggedIn = false }) {
  const handleGoogleLogin = useGoogleLogin({
    onSuccess: (tokenResponse) => {
      console.log(tokenResponse);
    },
    flow: "auth-code",
    onError: (error) => {
      console.log(error);
    },
  });

  const handleSwitchChange = (event) => {
    if (event.target.checked) {
      handleGoogleLogin();
    } else {
      googleLogout();
    }
  };

  return (
    <>
      <div className="d-flex mt-2">
        <div className="flex-shrink-0">
          <img className="me-1" src={googleSvg} alt="google" width="38" />
        </div>
        <div className="d-flex align-item-center justify-content-between flex-grow-1">
          <div className="me-1">
            <p className="fw-bolder mb-0">Outlook calendar</p>
            <span>Permitir que el bot acceda al calendario de outlook.</span>
          </div>
          <div className="mt-50 mt-sm-0">
            <div className="form-switch">
              <Input
                type="switch"
                checked={isLoggedIn}
                id={`account-google-calendar`}
                onChange={handleSwitchChange}
                disabled
              />
              <Label
                className="form-check-label"
                for={`account-google-calendar`}
              >
                <span className="switch-icon-left">
                  <Check size={14} />
                </span>
                <span className="switch-icon-right">
                  <X size={14} />
                </span>
              </Label>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}

export default OutlookAuth;
