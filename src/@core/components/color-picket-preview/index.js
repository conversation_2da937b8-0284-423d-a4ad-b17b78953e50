import { useEffect, useState } from "react";
import { Input } from "reactstrap";
import { ReactComponent as Logo } from "../../../assets/close-icon.svg";

import "./color-picket-preview.scss";

function ColorPickedPreview({ title, onColorChange, initValue }) {
  const [backgroundColor, setBackgroundColor] = useState(
    initValue.chatOpenIconBackgroundColor || "#000000"
  );
  const [textColor, setTextColor] = useState(
    initValue.chatOpenIconTextColor || "#ffffff"
  );

  const handleColorChange = () => {
    onColorChange(backgroundColor, textColor);
  };

  useEffect(() => {
    handleColorChange();
  }, [backgroundColor, textColor]);

  return (
    <div className="border rounded p-2">
      {title && <h4 className="mb-1">{title}</h4>}
      <div className="d-flex flex-row align-items-center gap-2 text-">
        <div
          style={{
            width: 46,
            height: 46,
            borderRadius: "100%",
            backgroundColor: backgroundColor,
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            fontSize: 32,
            color: textColor,
          }}
        >
          <Logo fill={textColor} />
        </div>

        <div className="divider-preview" />

        <div className="flex-fill">
          <div className="mb-2">
            <label htmlFor="color-select">Color de fondo del icono</label>
            <div className="color-picker-container d-flex align-items-stretch gap-1">
              <div className="mb-0">
                <Input
                  type="text"
                  name="closeIconBackgroundColor"
                  value={backgroundColor}
                  onChange={(e) => {
                    setBackgroundColor(e.target.value);
                    handleColorChange();
                  }}
                />
              </div>
              <input
                type="color"
                className="h-auto cursor-pointer"
                value={backgroundColor}
                name="primaryColor"
                onChange={(e) => {
                  setBackgroundColor(e.target.value);
                  handleColorChange();
                }}
              />
            </div>
          </div>
          <div className="mb-2">
            <label htmlFor="color-select">Color del texto en el icono</label>
            <div className="color-picker-container d-flex align-items-stretch gap-1">
              <div className="mb-0">
                <Input
                  type="text"
                  name="primaryColor"
                  value={textColor}
                  onChange={(e) => {
                    setTextColor(e.target.value);
                    handleColorChange();
                  }}
                />
              </div>
              <input
                type="color"
                className="h-auto cursor-pointer"
                value={textColor}
                name="primaryColor"
                onChange={(e) => {
                  setTextColor(e.target.value);
                  handleColorChange();
                }}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default ColorPickedPreview;
