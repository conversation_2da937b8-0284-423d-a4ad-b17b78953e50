export const protectRouterValidator = (requiredPermission) => {
  let userPermissionsList = [];
  const userPermission = JSON.parse(localStorage.getItem("userData")) || [];

  const getUniquePermissionList = (data) => {
    userPermissionsList = [];
    data &&
      data.map((roles) => {
        roles.permissions.map((item) => {
          if (!userPermissionsList.includes(item)) {
            userPermissionsList.push(item.name);
          }
        });
      });

    return Array.from(userPermissionsList);
  };
  const validatePermission = (permissionsList, permissionName) => {
    return permissionsList.includes(permissionName);
  };

  const completeList = getUniquePermissionList(userPermission.role);

  const havePermission = validatePermission(completeList, requiredPermission);
  return havePermission ? true : false;
};
