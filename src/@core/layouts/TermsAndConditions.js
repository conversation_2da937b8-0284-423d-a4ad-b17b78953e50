import { Card, CardBody, CardTitle } from "reactstrap";
import { useTranslation } from "react-i18next";

const TermsAndConditions = () => {
  const { t } = useTranslation();

  return (
    <div className="blank-page">
      <div className="app-content content">
        <div className="content-wrapper">
          <div className="content-body">
            <Card className="p-1 m-1">
              <CardTitle className="text-center text-xl" tag="h1">
                <h1>{t("termsAndConditions.title")}</h1>
              </CardTitle>
              <CardBody>
                {t("termsAndConditions.sections", { returnObjects: true }).map(
                  (section, index) => (
                    <div key={index}>
                      <h2 className="text-xl font-semibold mb-1">
                        {section.title}
                      </h2>
                      {section.content.map((paragraph, index) => (
                        <p key={index} className="mb-1">
                          {paragraph}
                        </p>
                      ))}
                    </div>
                  )
                )}
              </CardBody>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TermsAndConditions;
