import { useTranslation } from "react-i18next";

const Footer = () => {
  const { t } = useTranslation();

  return (
    <p className="clearfix mb-0">
      <span className="float-md-start d-block d-md-inline-block mt-25">
        COPYRIGHT © {new Date().getFullYear()}{" "}
        <a href="https://biitbot.com" target="_blank" rel="noopener noreferrer">
          Biitbot
        </a>
        <span className="d-none d-sm-inline-block">, {t("footer.text")}</span>
      </span>
    </p>
  );
};

export default Footer;
