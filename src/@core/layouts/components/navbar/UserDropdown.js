import { Link } from "react-router-dom";
import { useEffect, useState } from "react";
import { useDispatch } from "react-redux";
import { Power } from "react-feather";
import {
  UncontrolledDropdown,
  DropdownMenu,
  DropdownToggle,
  DropdownItem,
} from "reactstrap";

import Avatar from "@components/avatar";
import { isUserLoggedIn } from "@utils";
import { handleLogout } from "@store/authentication";
import { getUserData } from "../../../../auth/utils";
import { useTranslation } from "react-i18next";

const UserDropdown = () => {
  const dispatch = useDispatch();
  const { t } = useTranslation();

  const [userData, setUserData] = useState(null);

  useEffect(() => {
    if (isUserLoggedIn() !== null) {
      setUserData(getUserData());
    }
  }, []);

  return (
    <UncontrolledDropdown tag="li" className="dropdown-user nav-item">
      <DropdownToggle
        href="/"
        tag="a"
        className="nav-link dropdown-user-link"
        onClick={(e) => e.preventDefault()}
      >
        <div className="user-nav d-sm-flex d-none">
          <span className="user-name fw-bold">
            {userData && userData?.name?.toUpperCase()}
          </span>
        </div>
        <Avatar
          initials
          className="me-1"
          color={"light-primary"}
          content={(userData && userData?.name?.toUpperCase()) || "none"}
        />
      </DropdownToggle>
      <DropdownMenu end>
        <DropdownItem
          tag={Link}
          to="/login"
          onClick={() => dispatch(handleLogout())}
        >
          <Power size={14} className="me-75" />
          <span className="align-middle">{t("general.logOff")}</span>
        </DropdownItem>
      </DropdownMenu>
    </UncontrolledDropdown>
  );
};

export default UserDropdown;
