import { Sun, Moon } from 'react-feather'
import { NavItem, NavLink } from 'reactstrap'

import UserDropdown from './UserDropdown'

const NavbarUser = props => {
  const { skin, setSkin } = props

  const ThemeToggler = () => {
    if (skin === 'dark') {
      return <Sun className='ficon' onClick={() => setSkin('light')} />
    } else {
      return <Moon className='ficon' onClick={() => setSkin('dark')} />
    }
  }

  return (
    <ul className='nav navbar-nav align-items-center justify-content-between d-flex col'>
      <NavItem>
        <NavLink className='nav-link-style'>
          <ThemeToggler />
        </NavLink>
      </NavItem>
      <UserDropdown />
    </ul>
  )
}
export default NavbarUser
