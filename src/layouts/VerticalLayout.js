import { Outlet } from "react-router-dom";
import Layout from "@layouts/VerticalLayout";
import { useTranslation } from "react-i18next";
import {
  Circle,
  FileText,
  MessageSquare,
  Settings,
  ShoppingBag,
  Triangle,
  User,
  Users,
} from "react-feather";

const VerticalLayout = (props) => {
  const { t } = useTranslation();

  const routes = [
    {
      id: "hub",
      title: t("menu.hub"),
      icon: <Triangle size={20} />,
      navLink: "/hub",
      children: [
        {
          id: "hub-chat",
          title: t("menu.conversations"),
          icon: <MessageSquare size={12} />,
          navLink: "/hub/chat",
        },
        {
          id: "hub-contact",
          title: t("menu.contacts"),
          icon: <Users size={12} />,
          navLink: "/hub/contact",
        },
      ],
    },
    {
      id: "botSettings",
      title: t("menu.bot"),
      icon: <FileText size={20} />,
      children: [
        {
          id: "bot-list",
          title: t("menu.botList"),
          icon: <Circle size={12} />,
          navLink: "/apps/bot",
        },
        {
          id: "data-source-list",
          title: t("menu.dataSources"),
          icon: <Circle size={12} />,
          navLink: "/apps/data-source",
        },
      ],
    },
    {
      id: "order",
      title: t("menu.order"),
      icon: <ShoppingBag size={20} />,
      children: [
        {
          id: "order-list",
          title: t("menu.orderList"),
          icon: <Circle size={12} />,
          navLink: "/order/list",
        },
        {
          id: "products",
          title: t("menu.products"),
          icon: <Circle size={12} />,
          navLink: "/product/",
        },
      ],
    },
    {
      id: "settings",
      title: t("menu.settings"),
      icon: <Settings size={20} />,
      children: [
        {
          id: "list",
          title: t("menu.users"),
          icon: <User size={12} />,
          navLink: "/setting/users",
        },
        {
          id: "view",
          title: t("menu.company"),
          icon: <Circle size={12} />,
          navLink: "/setting/company",
        },
      ],
    },
  ];
  

  return (
    <Layout menuData={routes} {...props}>
      <Outlet />
    </Layout>
  );
};

export default VerticalLayout;
