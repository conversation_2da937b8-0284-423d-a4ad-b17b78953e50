import { io } from "socket.io-client";
import { ENV_SETTING } from "./constants";

const serverUrl = ENV_SETTING.VITE_WEBSOCKET_URL;

// Configuración mejorada del socket
export const socket = io(serverUrl, {
  transports: ["websocket"],
  reconnection: true,
  reconnectionAttempts: 5,
  reconnectionDelay: 1000,
  reconnectionDelayMax: 5000,
  timeout: 20000,
  autoConnect: false // Conectaremos manualmente después de configurar los listeners
});

// Manejo de eventos de conexión
const setupSocketListeners = () => {
  socket.on("connect", () => {
    console.log("WebSocket connected");
  });

  socket.on("disconnect", (reason) => {
    console.log("WebSocket disconnected:", reason);
  });

  socket.on("connect_error", (error) => {
    console.error("WebSocket connection error:", error.message);
  });
};

// Inicializar los listeners
export const initSocket = () => {
  setupSocketListeners();
  if (!socket.connected) {
    socket.connect();
  }
  return socket;
};
