import { Default<PERSON>oute } from "../router/routes";
import moment from "moment";

// ** Checks if an object is empty (returns boolean)
export const isObjEmpty = (obj) => Object.keys(obj).length === 0;

// ** Returns K format from a number
export const kFormatter = (num) =>
  num > 999 ? `${(num / 1000).toFixed(1)}k` : num;

// ** Converts HTML to string
export const htmlToString = (html) => html.replace(/<\/?[^>]+(>|$)/g, "");

// ** Checks if the passed date is today
const isToday = (date) => {
  const today = new Date();
  return (
    /* eslint-disable operator-linebreak */
    date.getDate() === today.getDate() &&
    date.getMonth() === today.getMonth() &&
    date.getFullYear() === today.getFullYear()
    /* eslint-enable */
  );
};

/**
 ** Format and return date in Humanize format
 ** Intl docs: https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Intl/DateTimeFormat/format
 ** Intl Constructor: https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Intl/DateTimeFormat/DateTimeFormat
 * @param {String} value date to format
 * @param {Object} formatting Intl object to format with
 */
export const formatDate = (
  value,
  formatting = { month: "short", day: "numeric", year: "numeric" }
) => {
  if (!value) return value;
  return new Intl.DateTimeFormat("en-US", formatting).format(new Date(value));
};

// ** Returns short month of passed date
export const formatDateToMonthShort = (value, toTimeForCurrentDay = true) => {
  const date = new Date(value);
  let formatting = { month: "short", day: "numeric" };

  if (toTimeForCurrentDay && isToday(date)) {
    formatting = { hour: "numeric", minute: "numeric" };
  }

  return new Intl.DateTimeFormat("en-US", formatting).format(new Date(value));
};

/**
 ** Return if user is logged in
 ** This is completely up to you and how you want to store the token in your frontend application
 *  ? e.g. If you are using cookies to store the application please update this function
 */
export const isUserLoggedIn = () => getUserData();
export const getUserData = () => {
  const userData = sessionStorage.getItem("userData");

  if (userData) {
    return JSON.parse(userData);
  }

  return getCookie("userData");
};

export const getLoginInfo = (propertyName) => {
  const data = sessionStorage.getItem(propertyName);

  if (data) {
    return JSON.parse(data);
  }

  return getCookie(propertyName);
};

/**
 ** This function is used for demo purpose route navigation
 ** In real app you won't need this function because your app will navigate to same route for each users regardless of ability
 ** Please note role field is just for showing purpose it's not used by anything in frontend
 ** We are checking role just for ease
 * ? NOTE: If you have different pages to navigate based on user ability then this function can be useful. However, you need to update it.
 * @param {String} userRole Role of user
 */
export const getHomeRouteForLoggedInUser = (userRole) => {
  if (userRole === "admin" || userRole === "employee") return DefaultRoute;
  return "/login";
};

// ** React Select Theme Colors
export const selectThemeColors = (theme) => ({
  ...theme,
  colors: {
    ...theme.colors,
    primary25: "#1389BB1a", // for option hover bg-color
    primary: "#1389BB", // for selected option bg-color
    neutral10: "#1389BB", // for tags bg-color
    neutral20: "#ededed", // for input border-color
    neutral30: "#ededed", // for input hover border-color
  },
});

export const countCharacters = (str) => {
  let count = 0;
  for (let i = 0; i < str.length; i++) {
    if ((str[i] >= "a" && str[i] <= "z") || (str[i] >= "A" && str[i] <= "Z")) {
      count++;
    }
  }
  return count;
};

export const countWords = (str) => {
  str.trim();
  return str.split(/\s+/).length;
};

export const FILE_MAX_CHARACTERS = 6000;
export const BOT_DESCRIPTION_MAX_CHARACTERS = 1000;
export const BOT_NAME_MAX_CHARACTERS = 15;

export function convertTo12HourFormat(time24) {
  console.log(time24);
  var parts = time24.split(":");
  var hours = parseInt(parts[0]);
  var minutes = parseInt(parts[1]);

  var suffix = hours >= 12 ? "PM" : "AM";

  if (hours > 12) {
    hours = hours - 12;
  } else if (hours === 0) {
    hours = 12;
  }

  var time12 =
    hours.toString().padStart(2, "0") +
    ":" +
    minutes.toString().padStart(2, "0") +
    " " +
    suffix;

  return time12;
}

export function validateUrl(url) {
  const pattern = new RegExp(
    "^(https?:\\/\\/)?" +
      "((([a-z\\d]([a-z\\d-]*[a-z\\d])*)\\.)+[a-z]{2,}|" +
      "((\\d{1,3}\\.){3}\\d{1,3}))" +
      "(\\:\\d+)?" +
      "(\\/[-a-z\\d%_.~+]*)*" +
      "(\\?[;&a-z\\d%_.~+=-]*)?" +
      "(\\#[-a-z\\d_]*)?$",
    "i"
  );

  return !!pattern.test(url);
}

export function getCookie(cookieName) {
  const cookiesString = document.cookie;

  const cookiesArray = cookiesString.split("; ");

  for (const cookie of cookiesArray) {
    const [name, value] = cookie.split("=");

    if (name === cookieName) {
      return decodeURIComponent(value);
    }
  }

  return null;
}

export function setCookie(cookieName, cookieValue, expirationDays) {
  const expirationDate = moment().add(expirationDays, "days");

  const expirationDateString = expirationDate.toDate().toUTCString();

  const cookieString = `${cookieName}=${encodeURIComponent(
    cookieValue
  )}; expires=${expirationDateString}; path=/`;

  document.cookie = cookieString;
}

export function deleteCookie(cookieName) {
  const expirationDate = new Date(0).toUTCString();

  const cookieString = `${cookieName}=; expires=${expirationDate}; path=/`;

  document.cookie = cookieString;
}

export function getUserRole() {
  return JSON.parse(localStorage.getItem("permissions")) || [];
}

export function setUserRoles(permissions) {
  localStorage.setItem("permissions", JSON.stringify(permissions));
}
