import { createSlice } from "@reduxjs/toolkit";
import { deleteCookie, setCookie } from "../utility/Utils";
import { getUserData } from "../auth/utils";

const initialUser = () => {
  return getUserData();
};

export const authSlice = createSlice({
  name: "authentication",
  initialState: {
    userData: initialUser(),
  },
  reducers: {
    handleLogout: (state) => {
      state.userData = {};
      localStorage.removeItem("userData");
      sessionStorage.removeItem("accessToken");
      sessionStorage.removeItem("userData");
      localStorage.removeItem("permissions");
      deleteCookie("accessToken");
      deleteCookie("userData");
    },
  },
});

export const { handleLogin, handleLogout } = authSlice.actions;

export default authSlice.reducer;
