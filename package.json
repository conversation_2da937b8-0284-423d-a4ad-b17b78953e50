{"name": "biitbot-admin", "version": "0.0.1", "private": true, "dependencies": {"@casl/ability": "^6.3.2", "@casl/react": "^3.1.0", "@esbuild-plugins/node-globals-polyfill": "^0.1.1", "@react-oauth/google": "^0.12.1", "@reduxjs/toolkit": "^1.2.5", "@supabase/auth-ui-react": "^0.4.7", "@supabase/auth-ui-shared": "^0.1.8", "@supabase/supabase-js": "^2.44.4", "animate.css": "4.1.1", "axios": "^1.1.3", "bootstrap": "^5.2.2", "bs-stepper": "1.7.0", "chart.js": "^4.0.1", "classnames": "^2.3.2", "cleave.js": "1.6.0", "file-saver": "^2.0.5", "flatpickr": "^4.6.13", "i18next": "^22.0.5", "i18next-browser-languagedetector": "^7.0.1", "i18next-http-backend": "^2.0.1", "moment": "^2.29.3", "nouislider-react": "^3.4.1", "postcss": "^8.4.35", "postcss-rtl": "^1.7.3", "prismjs": "^1.28.0", "qrcode.react": "^3.1.0", "react": "^18.2.0", "react-chartjs-2": "^5.0.1", "react-copy-to-clipboard": "^5.1.0", "react-country-flag": "^3.0.2", "react-data-table-component": "^7.5.2", "react-dom": "^18.2.0", "react-draft-wysiwyg": "^1.14.7", "react-dropzone": "^14.2.1", "react-easy-crop": "^5.0.0", "react-feather": "~2.0.3", "react-google-login": "^5.2.2", "react-hook-form": "^7.39.4", "react-hot-toast": "^2.4.0", "react-i18next": "^12.0.0", "react-markdown": "^8.0.7", "react-paginate": "^8.1.3", "react-perfect-scrollbar": "^1.5.8", "react-rating": "2.0.5", "react-redux": "^8.0.1", "react-router-dom": "^6.3.0", "react-select": "^5.6.1", "react-shepherd": "^4.1.0", "react-sortablejs": "6.0.0", "reactstrap": "^9.1.9", "rollup-plugin-node-polyfills": "^0.2.1", "sass": "^1.51.0", "socket.io-client": "^4.7.1", "sortablejs": "^1.15.2", "styled-components": "^6.1.8", "sweetalert2": "^11.4.14", "sweetalert2-react-content": "^5.0.0", "swiper": "^8.1.5", "vite-plugin-svgr": "^3.2.0", "wnumb": "1.2.0", "xlsx": "^0.18.5"}, "scripts": {"start": "vite --host ", "build": "NODE_OPTIONS=--max-old-space-size=12288 vite build", "preview": "vite preview", "format": "prettier --write \"src/**/*.js\"", "lint": "eslint src/**/*.js src/**/*.js", "serve": "./node_modules/.bin/serve -s dist", "lint:fix": "eslint src/**/*.js --fix"}, "resolutions": {"sass-loader/webpack": "^5.0.0", "recharts/prop-types": "^15.6.0", "styled-components/react-is": "^16.8.0", "react-hot-toast/goober/csstype": "^3.0.10", "html-to-draftjs/immutable": "^4.0.0", "postcss-custom-properties/postcss": "^8.2", "@esbuild-plugins/node-globals-polyfill/esbuild": "^0.15.15"}, "overrides": {"sass-loader": {"webpack": "^5.0.0"}, "recharts": {"prop-types": "^15.6.0"}, "styled-components": {"react-is": "^16.8.0"}, "react-hot-toast": {"goober": {"csstype": "^3.0.10"}}, "html-to-draftjs": {"immutable": "^4.0.0"}, "postcss-custom-properties": {"postcss": "^8.2"}, "@esbuild-plugins/node-globals-polyfill": {"esbuild": "^0.15.15"}}, "devDependencies": {"@babel/core": "^7.18.6", "@babel/eslint-parser": "^7.21.8", "@babel/preset-react": "^7.18.6", "@types/sortablejs": "^1.10.6", "@vitejs/plugin-react": "2.2.0", "eslint": "^8.39.0", "eslint-plugin-import": "^2.26.0", "eslint-plugin-react": "^7.32.2", "prettier": "^2.6.2", "react-syntax-highlighter": "^15.5.0", "vite": "^3.2.4"}, "browserslist": [">0.2%", "not dead", "not op_mini all"]}