module.exports = {
    "env": {
        "browser": true,
        "commonjs": true,
        "es2021": true,
        "es6": true,
    },
    "ecmaFeatures": {
        "modules": true,
        "spread": true,
        "restParams": true
    },
    "extends": [
        "eslint:recommended",
        "plugin:react/recommended"
    ],
    "overrides": [
    ],
    "parserOptions": {
        "ecmaVersion": "latest",
        "sourceType": "module"
    },
    "plugins": [
        "react"
    ],
    "rules": {
        "no-unused-vars": 2,
        "no-undef": 2,
        "react/react-in-jsx-scope": 0,
        "react/prop-types": 0,
    },
}
